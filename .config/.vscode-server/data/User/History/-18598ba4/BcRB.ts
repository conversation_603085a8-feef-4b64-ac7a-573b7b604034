import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { Resend } from "resend";
import {
  logContactFormSubmission,
  logNewsletterSubscription,
  createContactFormEntry,
  createNewsletterEntry
} from "./csvLogger";

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize Resend
  const resend = new Resend(process.env.RESEND_API_KEY);

  // Contact form submission endpoint
  app.post("/api/contact", async (req, res) => {
    try {
      const { name, email, company, project, budget, message } = req.body;

      // Validate required fields
      if (!name || !email || !message) {
        return res.status(400).json({
          success: false,
          message: "Name, email, and message are required fields."
        });
      }

      // Email 1: Confirmation email to form submitter
      const confirmationEmail = await resend.emails.send({
        from: "<EMAIL>",
        to: [email],
        subject: "Thank you for contacting <PERSON>am.tech - We'll be in touch soon",
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Thank you for contacting Beam.tech</title>
          </head>
          <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
              <!-- Header -->
              <div style="background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); padding: 40px 30px; text-align: center;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">
                  ⚡ beam.tech
                </h1>
                <p style="color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 16px;">
                  Web Design • Automation • AI Solutions
                </p>
              </div>

              <!-- Content -->
              <div style="padding: 40px 30px;">
                <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 24px;">
                  Thank you for reaching out, ${name}!
                </h2>

                <p style="color: #4b5563; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;">
                  We've received your inquiry and are excited to learn more about your project. Our team will review your message and get back to you within 24 hours.
                </p>

                <div style="background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin: 20px 0;">
                  <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px;">Your Inquiry Summary:</h3>
                  <p style="color: #4b5563; margin: 5px 0; font-size: 14px;"><strong>Project Type:</strong> ${project || 'Not specified'}</p>
                  <p style="color: #4b5563; margin: 5px 0; font-size: 14px;"><strong>Budget Range:</strong> ${budget || 'Not specified'}</p>
                  ${company ? `<p style="color: #4b5563; margin: 5px 0; font-size: 14px;"><strong>Company:</strong> ${company}</p>` : ''}
                </div>

                <p style="color: #4b5563; line-height: 1.6; margin: 20px 0; font-size: 16px;">
                  In the meantime, feel free to explore our portfolio and learn more about how we've helped other businesses transform their digital presence.
                </p>

                <div style="text-align: center; margin: 30px 0;">
                  <a href="https://beam.tech" style="display: inline-block; background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; font-size: 16px;">
                    Visit Our Website
                  </a>
                </div>
              </div>

              <!-- Footer -->
              <div style="background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; margin: 0 0 10px 0; font-size: 14px;">
                  Made with ❤️ in Margaret River and Canberra, Australia
                </p>
                <p style="color: #9ca3af; margin: 0; font-size: 12px;">
                  This email was sent because you contacted us through our website. If you have any questions, reply to this email.
                </p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
          Thank you for contacting Beam.tech, ${name}!

          We've received your inquiry and will get back to you within 24 hours.

          Your inquiry summary:
          - Project Type: ${project || 'Not specified'}
          - Budget Range: ${budget || 'Not specified'}
          ${company ? `- Company: ${company}` : ''}

          Best regards,
          The Beam.tech Team

          Made with ❤️ in Margaret River and Canberra, Australia
        `
      });

      // Email 2: Notification email to business owner
      const notificationEmail = await resend.emails.send({
        from: "<EMAIL>",
        to: [process.env.NOTIFICATION_EMAIL || "<EMAIL>"],
        subject: `New Contact Form Submission from ${name}`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New Contact Form Submission</title>
          </head>
          <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
              <!-- Header -->
              <div style="background: linear-gradient(135deg, #dc2626 0%, #ea580c 100%); padding: 30px; text-align: center;">
                <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">
                  🚨 New Contact Form Submission
                </h1>
                <p style="color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 14px;">
                  beam.tech website
                </p>
              </div>

              <!-- Content -->
              <div style="padding: 30px;">
                <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px;">
                  Contact Details
                </h2>

                <div style="background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin: 20px 0;">
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="padding: 8px 0; font-weight: bold; color: #374151; width: 120px;">Name:</td>
                      <td style="padding: 8px 0; color: #1f2937;">${name}</td>
                    </tr>
                    <tr>
                      <td style="padding: 8px 0; font-weight: bold; color: #374151;">Email:</td>
                      <td style="padding: 8px 0; color: #1f2937;"><a href="mailto:${email}" style="color: #2563eb; text-decoration: none;">${email}</a></td>
                    </tr>
                    ${company ? `
                    <tr>
                      <td style="padding: 8px 0; font-weight: bold; color: #374151;">Company:</td>
                      <td style="padding: 8px 0; color: #1f2937;">${company}</td>
                    </tr>
                    ` : ''}
                    <tr>
                      <td style="padding: 8px 0; font-weight: bold; color: #374151;">Project Type:</td>
                      <td style="padding: 8px 0; color: #1f2937;">${project || 'Not specified'}</td>
                    </tr>
                    <tr>
                      <td style="padding: 8px 0; font-weight: bold; color: #374151;">Budget:</td>
                      <td style="padding: 8px 0; color: #1f2937;">${budget || 'Not specified'}</td>
                    </tr>
                  </table>
                </div>

                <h3 style="color: #1f2937; margin: 30px 0 15px 0; font-size: 18px;">Message:</h3>
                <div style="background-color: #f9fafb; border-left: 4px solid #2563eb; padding: 20px; margin: 0 0 20px 0;">
                  <p style="color: #374151; line-height: 1.6; margin: 0; white-space: pre-wrap;">${message}</p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                  <a href="mailto:${email}?subject=Re: Your inquiry to Beam.tech" style="display: inline-block; background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; font-size: 16px;">
                    Reply to ${name}
                  </a>
                </div>
              </div>

              <!-- Footer -->
              <div style="background-color: #f9fafb; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; margin: 0; font-size: 12px;">
                  Submitted on ${new Date().toLocaleString('en-AU', { timeZone: 'Australia/Perth' })} (Perth time)
                </p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
          New Contact Form Submission from ${name}

          Contact Details:
          - Name: ${name}
          - Email: ${email}
          ${company ? `- Company: ${company}` : ''}
          - Project Type: ${project || 'Not specified'}
          - Budget: ${budget || 'Not specified'}

          Message:
          ${message}

          Submitted on ${new Date().toLocaleString('en-AU', { timeZone: 'Australia/Perth' })} (Perth time)
        `
      });

      console.log('Confirmation email sent:', confirmationEmail);
      console.log('Notification email sent:', notificationEmail);

      // Log successful contact form submission to CSV
      const contactEntry = createContactFormEntry(
        name,
        email,
        company,
        project,
        budget,
        message,
        'success'
      );
      await logContactFormSubmission(contactEntry);

      res.json({
        success: true,
        message: "Thank you for your message! We've sent you a confirmation email and will get back to you within 24 hours."
      });

    } catch (error) {
      console.error('Email sending error:', error);

      // Log failed contact form submission to CSV
      const failedContactEntry = createContactFormEntry(
        req.body.name || '',
        req.body.email || '',
        req.body.company || '',
        req.body.project || '',
        req.body.budget || '',
        req.body.message || '',
        'failed'
      );
      await logContactFormSubmission(failedContactEntry);

      res.status(500).json({
        success: false,
        message: "Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.",
        error: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : String(error)) : undefined
      });
    }
  });

  // Newsletter subscription endpoint
  app.post("/api/newsletter", async (req, res) => {
    try {
      const { email } = req.body;

      // Validate required fields
      if (!email) {
        return res.status(400).json({
          success: false,
          message: "Email is required."
        });
      }

      // Send welcome email to subscriber
      const welcomeEmail = await resend.emails.send({
        from: "<EMAIL>",
        to: [email],
        subject: "Welcome to Beam.tech Newsletter! 🚀",
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Beam.tech Newsletter</title>
          </head>
          <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
              <!-- Header -->
              <div style="background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); padding: 40px 30px; text-align: center;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">
                  ⚡ beam.tech
                </h1>
                <p style="color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 16px;">
                  Welcome to our newsletter!
                </p>
              </div>

              <!-- Content -->
              <div style="padding: 40px 30px;">
                <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 24px;">
                  🎉 Welcome aboard!
                </h2>

                <p style="color: #4b5563; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;">
                  Thank you for subscribing to the Beam.tech newsletter! You'll now receive the latest insights on:
                </p>

                <div style="background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin: 20px 0;">
                  <ul style="color: #4b5563; margin: 0; padding-left: 20px;">
                    <li style="margin-bottom: 8px;">🎨 <strong>Web Design Trends</strong> - Latest design patterns and user experience insights</li>
                    <li style="margin-bottom: 8px;">🤖 <strong>Business Automation</strong> - Tools and strategies to streamline your operations</li>
                    <li style="margin-bottom: 8px;">🧠 <strong>AI Solutions</strong> - Practical applications of artificial intelligence for business</li>
                    <li style="margin-bottom: 8px;">💡 <strong>Tech Innovation</strong> - Emerging technologies and industry updates</li>
                  </ul>
                </div>

                <p style="color: #4b5563; line-height: 1.6; margin: 20px 0; font-size: 16px;">
                  We promise to deliver valuable content and never spam your inbox. You can unsubscribe at any time.
                </p>

                <div style="text-align: center; margin: 30px 0;">
                  <a href="https://beam.tech" style="display: inline-block; background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; font-size: 16px;">
                    Visit Our Website
                  </a>
                </div>
              </div>

              <!-- Footer -->
              <div style="background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; margin: 0 0 10px 0; font-size: 14px;">
                  Made with ❤️ in Margaret River and Canberra, Australia
                </p>
                <p style="color: #9ca3af; margin: 0; font-size: 12px;">
                  You're receiving this because you subscribed to our newsletter at beam.tech
                </p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
          Welcome to Beam.tech Newsletter!

          Thank you for subscribing! You'll now receive the latest insights on:

          • Web Design Trends - Latest design patterns and user experience insights
          • Business Automation - Tools and strategies to streamline your operations
          • AI Solutions - Practical applications of artificial intelligence for business
          • Tech Innovation - Emerging technologies and industry updates

          We promise to deliver valuable content and never spam your inbox.

          Visit our website: https://beam.tech

          Made with ❤️ in Margaret River and Canberra, Australia
        `
      });

      // Send notification to admin about new subscriber
      const notificationEmail = await resend.emails.send({
        from: "<EMAIL>",
        to: [process.env.NOTIFICATION_EMAIL || "<EMAIL>"],
        subject: `📧 New Newsletter Subscriber: ${email}`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New Newsletter Subscriber</title>
          </head>
          <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
              <!-- Header -->
              <div style="background: linear-gradient(135deg, #16a34a 0%, #**********%); padding: 30px; text-align: center;">
                <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">
                  📧 New Newsletter Subscriber
                </h1>
                <p style="color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 14px;">
                  beam.tech website
                </p>
              </div>

              <!-- Content -->
              <div style="padding: 30px;">
                <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px;">
                  Subscriber Details
                </h2>

                <div style="background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin: 20px 0;">
                  <p style="color: #374151; margin: 0; font-size: 16px;">
                    <strong>Email:</strong> <a href="mailto:${email}" style="color: #2563eb; text-decoration: none;">${email}</a>
                  </p>
                  <p style="color: #6b7280; margin: 10px 0 0 0; font-size: 14px;">
                    Subscribed on: ${new Date().toLocaleString('en-AU', { timeZone: 'Australia/Perth' })} (Perth time)
                  </p>
                </div>

                <p style="color: #4b5563; line-height: 1.6; margin: 20px 0; font-size: 16px;">
                  A welcome email has been automatically sent to the subscriber.
                </p>
              </div>

              <!-- Footer -->
              <div style="background-color: #f9fafb; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; margin: 0; font-size: 12px;">
                  Newsletter subscription from beam.tech
                </p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
          New Newsletter Subscriber

          Email: ${email}
          Subscribed on: ${new Date().toLocaleString('en-AU', { timeZone: 'Australia/Perth' })} (Perth time)

          A welcome email has been automatically sent to the subscriber.
        `
      });

      console.log('Welcome email sent:', welcomeEmail);
      console.log('Admin notification sent:', notificationEmail);

      // Log successful newsletter subscription to CSV
      const newsletterEntry = createNewsletterEntry(email, 'success');
      await logNewsletterSubscription(newsletterEntry);

      res.json({
        success: true,
        message: "Successfully subscribed! Check your email for a welcome message."
      });

    } catch (error) {
      console.error('Newsletter subscription error:', error);

      // Log failed newsletter subscription to CSV
      const failedNewsletterEntry = createNewsletterEntry(req.body.email || '', 'failed');
      await logNewsletterSubscription(failedNewsletterEntry);

      res.status(500).json({
        success: false,
        message: "Sorry, there was an error with your subscription. Please try again.",
        error: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : String(error)) : undefined
      });
    }
  });

  // use storage to perform CRUD operations on the storage interface
  // e.g. storage.insertUser(user) or storage.getUserByUsername(username)

  const httpServer = createServer(app);

  return httpServer;
}
