// Test script to verify text logging functionality

import fs from 'fs';
import path from 'path';
import {
  logContactFormSubmission,
  logNewsletterSubscription,
  createContactFormEntry,
  createNewsletterEntry,
  ensureLogFiles
} from './csvLogger';

async function testLogging() {
  console.log('🧪 Starting logging system test...');
  
  try {
    // 1. Initialize log files
    console.log('📁 Initializing log files...');
    await ensureLogFiles();
    
    // 2. Create test contact entry
    const testContact = createContactFormEntry(
      'Test User',
      '<EMAIL>',
      'Test Company',
      'web-design',
      '5k-15k',
      'This is a test message with commas, quotes, and new\nlines.',
      'success'
    );
    
    // 3. Create test newsletter entry
    const testNewsletter = createNewsletterEntry(
      '<EMAIL>',
      'success'
    );
    
    // 4. Log test entries
    console.log('📝 Logging test contact submission...');
    await logContactFormSubmission(testContact);
    
    console.log('📝 Logging test newsletter subscription...');
    await logNewsletterSubscription(testNewsletter);
    
    // 5. Verify log files exist and have content
    const serverDir = path.resolve(__dirname);
    const contactLogPath = path.join(serverDir, 'logs', 'contact-form-submissions.txt');
    const newsletterLogPath = path.join(serverDir, 'logs', 'newsletter-subscriptions.txt');
    
    // 6. Read logs and print content
    console.log('📄 Reading contact log file...');
    const contactLog = fs.existsSync(contactLogPath) 
      ? fs.readFileSync(contactLogPath, 'utf8')
      : 'FILE NOT FOUND';
    
    console.log('📄 Reading newsletter log file...');
    const newsletterLog = fs.existsSync(newsletterLogPath)
      ? fs.readFileSync(newsletterLogPath, 'utf8')
      : 'FILE NOT FOUND';
    
    console.log('\n✅ TEST RESULTS:\n');
    console.log('Contact log exists:', fs.existsSync(contactLogPath));
    console.log('Newsletter log exists:', fs.existsSync(newsletterLogPath));
    console.log('\nContact log contents:');
    console.log('====================');
    console.log(contactLog);
    console.log('\nNewsletter log contents:');
    console.log('====================');
    console.log(newsletterLog);
    
    console.log('\n✅ Logging test completed successfully!');
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testLogging();
