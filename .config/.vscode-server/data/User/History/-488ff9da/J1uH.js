// Test script for Resend email service
import { Resend } from "resend";
import dotenv from "dotenv";

dotenv.config();

const resend = new Resend(process.env.RESEND_API_KEY);

async function testResendEmail() {
  try {
    console.log("🧪 Testing Resend email service...");
    console.log("API Key:", process.env.RESEND_API_KEY ? "✅ Found" : "❌ Missing");
    
    const result = await resend.emails.send({
      from: "<EMAIL>",
      to: ["<EMAIL>"],
      subject: "✅ Resend Test - Beam.tech Contact Form Integration",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); padding: 30px; text-align: center; color: white;">
            <h1>🎉 Resend Integration Test</h1>
            <p>beam.tech Contact Form</p>
          </div>
          
          <div style="padding: 30px; background: #f9fafb;">
            <h2>✅ Integration Status: SUCCESS!</h2>
            <p>This email confirms that your Resend integration is working correctly.</p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>Test Details:</h3>
              <ul>
                <li><strong>API Key:</strong> Valid ✅</li>
                <li><strong>From Address:</strong> <EMAIL></li>
                <li><strong>Service:</strong> Resend (much simpler than MailerSend!)</li>
                <li><strong>Test Time:</strong> ${new Date().toISOString()}</li>
              </ul>
            </div>
            
            <div style="background: #dcfce7; padding: 15px; border-radius: 8px; border-left: 4px solid #16a34a;">
              <h4>🎉 Ready for Production:</h4>
              <p>Your beam.tech contact form is now ready to send emails via Resend!</p>
              <p>No domain verification required - it just works!</p>
            </div>
          </div>
          
          <div style="background: #374151; color: white; padding: 20px; text-align: center;">
            <p>Made with ❤️ in Margaret River and Canberra, Australia</p>
          </div>
        </div>
      `,
      text: `
        Resend Integration Test - SUCCESS!
        
        This email confirms that your Resend integration is working correctly.
        
        Test Details:
        - API Key: Valid
        - From Address: <EMAIL>
        - Service: Resend (much simpler than MailerSend!)
        - Test Time: ${new Date().toISOString()}
        
        Ready for Production:
        Your beam.tech contact form is now ready to send emails via Resend!
        No domain verification required - it just works!
        
        Made with ❤️ in Margaret River and Canberra, Australia
      `
    });

    console.log("✅ Email sent successfully!");
    console.log("Message ID:", result.data?.id || 'N/A');
    console.log("Response:", result);
    console.log("\n🎉 SUCCESS! Check <EMAIL> for the test email.");
    console.log("\n📋 Your contact form is ready to use!");
    
  } catch (error) {
    console.error("❌ Email sending failed:");
    console.error("Error:", error);
    
    if (error.message?.includes('API key')) {
      console.log("\n🔧 SOLUTION:");
      console.log("Check that your Resend API key is correct in the .env file");
    }
  }
}

testResendEmail();
