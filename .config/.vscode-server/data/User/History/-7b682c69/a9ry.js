// Test script for MailerSend TRIAL account
import { <PERSON><PERSON><PERSON><PERSON>, Email<PERSON>ara<PERSON>, Sender, Recipient } from "mailersend";
import dotenv from "dotenv";

dotenv.config();

const mailerSend = new MailerSend({
  apiKey: process.env.MAILERSEND_API_TOKEN || "mlsn.2d97cb9a76439b49d03b4ecd2239f3ad6874b7758fb206916c49df25c35de2e2",
});

async function testTrialEmail() {
  try {
    console.log("🧪 Testing MailerSend TRIAL account...");
    console.log("API Token:", process.env.MAILERSEND_API_TOKEN ? "✅ Found" : "❌ Missing");
    
    // Use the admin email from environment (the email used to register MailerSend)
    const adminEmail = process.env.MAILERSEND_ADMIN_EMAIL || "<EMAIL>";
    const emailParams = new EmailParams()
      .setFrom(new Sender("<EMAIL>", "Beam.tech Test"))
      .setTo([new Recipient(adminEmail, "Eddie")]) // Using MailerSend admin email
      .setSubject("✅ MailerSend Test - Contact Form Integration")
      .setHtml(`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); padding: 30px; text-align: center; color: white;">
            <h1>🎉 MailerSend Integration Test</h1>
            <p>beam.tech Contact Form</p>
          </div>
          
          <div style="padding: 30px; background: #f9fafb;">
            <h2>✅ Integration Status: SUCCESS!</h2>
            <p>This email confirms that your MailerSend integration is working correctly.</p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>Test Details:</h3>
              <ul>
                <li><strong>API Token:</strong> Valid ✅</li>
                <li><strong>From Address:</strong> <EMAIL></li>
                <li><strong>Account Type:</strong> Trial (limited to admin email)</li>
                <li><strong>Test Time:</strong> ${new Date().toISOString()}</li>
              </ul>
            </div>
            
            <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h4>📋 Next Steps for Production:</h4>
              <ol>
                <li>Verify domain "beamtradingx.com" in MailerSend dashboard</li>
                <li>Upgrade from trial account (if needed)</li>
                <li>Test contact form on website</li>
              </ol>
            </div>
            
            <p style="margin-top: 30px;">
              <strong>Contact Form Ready:</strong> Your beam.tech contact form is now configured to send emails via MailerSend!
            </p>
          </div>
          
          <div style="background: #374151; color: white; padding: 20px; text-align: center;">
            <p>Made with ❤️ in Margaret River and Canberra, Australia</p>
          </div>
        </div>
      `)
      .setText(`
        MailerSend Integration Test - SUCCESS!
        
        This email confirms that your MailerSend integration is working correctly.
        
        Test Details:
        - API Token: Valid
        - From Address: <EMAIL>
        - Account Type: Trial (limited to admin email)
        - Test Time: ${new Date().toISOString()}
        
        Next Steps for Production:
        1. Verify domain "beamtradingx.com" in MailerSend dashboard
        2. Upgrade from trial account (if needed)
        3. Test contact form on website
        
        Your beam.tech contact form is now configured to send emails via MailerSend!
        
        Made with ❤️ in Margaret River and Canberra, Australia
      `);

    console.log("📧 Attempting to send test email to admin account...");
    const result = await mailerSend.email.send(emailParams);
    console.log("✅ Email sent successfully to admin account!");
    console.log("Message ID:", result.headers?.['x-message-id'] || 'N/A');
    console.log("\n🎉 SUCCESS! Check your email inbox.");
    console.log("\n📋 Next Steps:");
    console.log("1. Check <EMAIL> for the test email");
    console.log("2. Your contact form is ready to use!");
    console.log("3. Set up email <NAME_EMAIL> to <EMAIL> if needed");
    
  } catch (error) {
    console.error("❌ Email sending failed:");
    console.error("Status Code:", error.statusCode);
    console.error("Error Message:", error.body?.message);
    
    if (error.body?.message?.includes("administrator's email")) {
      console.log("\n🔧 SOLUTION:");
      console.log("<NAME_EMAIL> exists and is configured to receive emails.");
      console.log("Using verified domain addresses for both sender and recipient.");
    }
    
    console.log("\nFull error details:", error);
  }
}

testTrialEmail();
