import fs from 'fs';
import path from 'path';

// CSV logging utility for contact forms and newsletter subscriptions

export interface ContactFormEntry {
  timestamp: string;
  name: string;
  email: string;
  company: string;
  project_type: string;
  budget: string;
  message: string;
  submission_status: 'success' | 'failed';
}

export interface NewsletterEntry {
  timestamp: string;
  email: string;
  subscription_status: 'success' | 'failed';
}

// Escape CSV values to prevent corruption from commas, quotes, and newlines
function escapeCsvValue(value: string): string {
  if (!value) return '';
  
  // Convert to string and handle null/undefined
  const stringValue = String(value);
  
  // If the value contains comma, quote, or newline, wrap in quotes and escape internal quotes
  if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n') || stringValue.includes('\r')) {
    return `"${stringValue.replace(/"/g, '""')}"`;
  }
  
  return stringValue;
}

// Convert object to CSV row
function objectToCsvRow(obj: Record<string, any>): string {
  return Object.values(obj).map(value => escapeCsvValue(String(value))).join(',');
}

// Ensure CSV file exists with headers
async function ensureCsvFile(filePath: string, headers: string[]): Promise<void> {
  try {
    // Check if file exists
    await fs.promises.access(filePath);
  } catch (error) {
    // File doesn't exist, create it with headers
    const headerRow = headers.map(header => escapeCsvValue(header)).join(',');
    await fs.promises.writeFile(filePath, headerRow + '\n', 'utf8');
    console.log(`Created CSV file: ${filePath}`);
  }
}

// Append data to CSV file
async function appendToCsv(filePath: string, data: string): Promise<void> {
  try {
    await fs.promises.appendFile(filePath, data + '\n', 'utf8');
  } catch (error) {
    console.error(`Error appending to CSV file ${filePath}:`, error);
    throw error;
  }
}

// Log contact form submission
export async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {
  try {
    // Use __dirname equivalent for ES modules
    const serverDir = path.dirname(new URL(import.meta.url).pathname);
    const csvDir = path.join(serverDir, 'logs');
    const csvFile = path.join(csvDir, 'contact-enquiries.csv');
    
    // Ensure logs directory exists
    await fs.promises.mkdir(csvDir, { recursive: true });
    
    // Define headers for contact form CSV
    const headers = [
      'timestamp',
      'name', 
      'email',
      'company',
      'project_type',
      'budget',
      'message',
      'submission_status'
    ];
    
    // Ensure CSV file exists with headers
    await ensureCsvFile(csvFile, headers);
    
    // Convert entry to CSV row
    const csvRow = objectToCsvRow(entry);
    
    // Append to file
    await appendToCsv(csvFile, csvRow);
    
    console.log(`Contact form submission logged: ${entry.email}`);
  } catch (error) {
    console.error('Error logging contact form submission:', error);
    // Don't throw error to avoid breaking the main flow
  }
}

// Log newsletter subscription
export async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {
  try {
    // Use __dirname equivalent for ES modules
    const serverDir = path.dirname(new URL(import.meta.url).pathname);
    const csvDir = path.join(serverDir, 'logs');
    const csvFile = path.join(csvDir, 'newsletter-subscriptions.csv');
    
    // Ensure logs directory exists
    await fs.promises.mkdir(csvDir, { recursive: true });
    
    // Define headers for newsletter CSV
    const headers = [
      'timestamp',
      'email',
      'subscription_status'
    ];
    
    // Ensure CSV file exists with headers
    await ensureCsvFile(csvFile, headers);
    
    // Convert entry to CSV row
    const csvRow = objectToCsvRow(entry);
    
    // Append to file
    await appendToCsv(csvFile, csvRow);
    
    console.log(`Newsletter subscription logged: ${entry.email}`);
  } catch (error) {
    console.error('Error logging newsletter subscription:', error);
    // Don't throw error to avoid breaking the main flow
  }
}

// Helper function to get current ISO timestamp
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

// Helper function to create contact form entry
export function createContactFormEntry(
  name: string,
  email: string,
  company: string,
  project: string,
  budget: string,
  message: string,
  status: 'success' | 'failed'
): ContactFormEntry {
  return {
    timestamp: getCurrentTimestamp(),
    name: name || '',
    email: email || '',
    company: company || '',
    project_type: project || '',
    budget: budget || '',
    message: message || '',
    submission_status: status
  };
}

// Helper function to create newsletter entry
export function createNewsletterEntry(
  email: string,
  status: 'success' | 'failed'
): NewsletterEntry {
  return {
    timestamp: getCurrentTimestamp(),
    email: email || '',
    subscription_status: status
  };
}
