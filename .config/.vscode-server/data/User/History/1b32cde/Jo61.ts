import fs from 'fs';
import path from 'path';

// Simple text logging utility for contact forms and newsletter subscriptions

export interface ContactFormEntry {
  timestamp: string;
  name: string;
  email: string;
  company: string;
  project_type: string;
  budget: string;
  message: string;
  submission_status: 'success' | 'failed';
}

export interface NewsletterEntry {
  timestamp: string;
  email: string;
  subscription_status: 'success' | 'failed';
}

// Format contact form entry as text line
function formatContactFormEntry(entry: ContactFormEntry): string {
  const cleanMessage = entry.message.replace(/\n/g, ' ').replace(/\r/g, ' ').substring(0, 200);
  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;
}

// Format newsletter entry as text line
function formatNewsletterEntry(entry: NewsletterEntry): string {
  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;
}

// Ensure log directory exists
async function ensureLogDirectory(logDir: string): Promise<void> {
  try {
    await fs.promises.mkdir(logDir, { recursive: true });
    console.log(`📁 Log directory ensured: ${logDir}`);
  } catch (error) {
    console.error(`❌ Error creating log directory ${logDir}:`, error);
    throw error;
  }
}

// Append data to text file
async function appendToTextFile(filePath: string, data: string): Promise<void> {
  try {
    await fs.promises.appendFile(filePath, data + '\n', 'utf8');
    console.log(`📝 Successfully appended to: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error appending to text file ${filePath}:`, error);
    throw error;
  }
}

// Log contact form submission
export async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {
  console.log(`🔍 DEBUG: logContactFormSubmission called for ${entry.email}`);

  let textFile = '';
  let logDir = '';

  try {
    // Use __dirname equivalent for ES modules
    const serverDir = path.dirname(new URL(import.meta.url).pathname);
    logDir = path.join(serverDir, 'logs');
    textFile = path.join(logDir, 'contact-form-submissions.txt');

    console.log(`🔍 DEBUG: Log directory: ${logDir}`);
    console.log(`🔍 DEBUG: Text file path: ${textFile}`);

    // Ensure logs directory exists
    await ensureLogDirectory(logDir);

    // Format entry as text line
    const textLine = formatContactFormEntry(entry);
    console.log(`🔍 DEBUG: Formatted text line: ${textLine}`);

    // Append to file
    await appendToTextFile(textFile, textLine);

    console.log(`✅ Contact form submission logged: ${entry.email} to ${textFile}`);
  } catch (error) {
    console.error('❌ Error logging contact form submission:', error);
    console.error('Text file path:', textFile);
    console.error('Log directory:', logDir);
    // Don't throw error to avoid breaking the main flow
  }
}

// Log newsletter subscription
export async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {
  console.log(`🔍 DEBUG: logNewsletterSubscription called for ${entry.email}`);

  let textFile = '';
  let logDir = '';

  try {
    // Use __dirname equivalent for ES modules
    const serverDir = path.dirname(new URL(import.meta.url).pathname);
    logDir = path.join(serverDir, 'logs');
    textFile = path.join(logDir, 'newsletter-subscriptions.txt');

    console.log(`🔍 DEBUG: Log directory: ${logDir}`);
    console.log(`🔍 DEBUG: Text file path: ${textFile}`);

    // Ensure logs directory exists
    await ensureLogDirectory(logDir);

    // Format entry as text line
    const textLine = formatNewsletterEntry(entry);
    console.log(`🔍 DEBUG: Formatted text line: ${textLine}`);

    // Append to file
    await appendToTextFile(textFile, textLine);

    console.log(`✅ Newsletter subscription logged: ${entry.email} to ${textFile}`);
  } catch (error) {
    console.error('❌ Error logging newsletter subscription:', error);
    console.error('Text file path:', textFile);
    console.error('Log directory:', logDir);
    // Don't throw error to avoid breaking the main flow
  }
}

// Helper function to get current ISO timestamp
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

// Helper function to create contact form entry
export function createContactFormEntry(
  name: string,
  email: string,
  company: string,
  project: string,
  budget: string,
  message: string,
  status: 'success' | 'failed'
): ContactFormEntry {
  console.log(`🔍 DEBUG: createContactFormEntry called for ${email} with status ${status}`);
  return {
    timestamp: getCurrentTimestamp(),
    name: name || '',
    email: email || '',
    company: company || '',
    project_type: project || '',
    budget: budget || '',
    message: message || '',
    submission_status: status
  };
}

// Helper function to create newsletter entry
export function createNewsletterEntry(
  email: string,
  status: 'success' | 'failed'
): NewsletterEntry {
  console.log(`🔍 DEBUG: createNewsletterEntry called for ${email} with status ${status}`);
  return {
    timestamp: getCurrentTimestamp(),
    email: email || '',
    subscription_status: status
  };
}
