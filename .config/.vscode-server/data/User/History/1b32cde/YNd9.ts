import fs from 'fs';
import path from 'path';

// Simple text logging utility for contact forms and newsletter subscriptions

export interface ContactFormEntry {
  timestamp: string;
  name: string;
  email: string;
  company: string;
  project_type: string;
  budget: string;
  message: string;
  submission_status: 'success' | 'failed';
}

export interface NewsletterEntry {
  timestamp: string;
  email: string;
  subscription_status: 'success' | 'failed';
}

// Format contact form entry as text line
function formatContactFormEntry(entry: ContactFormEntry): string {
  const cleanMessage = entry.message.replace(/\n/g, ' ').replace(/\r/g, ' ').substring(0, 200);
  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;
}

// Format newsletter entry as text line
function formatNewsletterEntry(entry: NewsletterEntry): string {
  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;
}

// Ensure log directory exists
async function ensureLogDirectory(logDir: string): Promise<void> {
  try {
    await fs.promises.mkdir(logDir, { recursive: true });
    console.log(`📁 Log directory ensured: ${logDir}`);
  } catch (error) {
    console.error(`❌ Error creating log directory ${logDir}:`, error);
    throw error;
  }
}

// Append data to text file
async function appendToTextFile(filePath: string, data: string): Promise<void> {
  try {
    await fs.promises.appendFile(filePath, data + '\n', 'utf8');
    console.log(`📝 Successfully appended to: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error appending to text file ${filePath}:`, error);
    throw error;
  }
}

// Log contact form submission
export async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {
  let csvFile = '';
  let csvDir = '';

  try {
    // Use __dirname equivalent for ES modules
    const serverDir = path.dirname(new URL(import.meta.url).pathname);
    csvDir = path.join(serverDir, 'logs');
    csvFile = path.join(csvDir, 'contact-enquiries.csv');

    // Ensure logs directory exists
    await fs.promises.mkdir(csvDir, { recursive: true });

    // Define headers for contact form CSV
    const headers = [
      'timestamp',
      'name',
      'email',
      'company',
      'project_type',
      'budget',
      'message',
      'submission_status'
    ];

    // Ensure CSV file exists with headers
    await ensureCsvFile(csvFile, headers);

    // Convert entry to CSV row
    const csvRow = objectToCsvRow(entry);

    // Append to file
    await appendToCsv(csvFile, csvRow);

    console.log(`Contact form submission logged: ${entry.email} to ${csvFile}`);
  } catch (error) {
    console.error('Error logging contact form submission:', error);
    console.error('CSV file path:', csvFile);
    console.error('CSV directory:', csvDir);
    // Don't throw error to avoid breaking the main flow
  }
}

// Log newsletter subscription
export async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {
  let csvFile = '';
  let csvDir = '';

  try {
    // Use __dirname equivalent for ES modules
    const serverDir = path.dirname(new URL(import.meta.url).pathname);
    csvDir = path.join(serverDir, 'logs');
    csvFile = path.join(csvDir, 'newsletter-subscriptions.csv');

    // Ensure logs directory exists
    await fs.promises.mkdir(csvDir, { recursive: true });

    // Define headers for newsletter CSV
    const headers = [
      'timestamp',
      'email',
      'subscription_status'
    ];

    // Ensure CSV file exists with headers
    await ensureCsvFile(csvFile, headers);

    // Convert entry to CSV row
    const csvRow = objectToCsvRow(entry);

    // Append to file
    await appendToCsv(csvFile, csvRow);

    console.log(`Newsletter subscription logged: ${entry.email} to ${csvFile}`);
  } catch (error) {
    console.error('Error logging newsletter subscription:', error);
    console.error('CSV file path:', csvFile);
    console.error('CSV directory:', csvDir);
    // Don't throw error to avoid breaking the main flow
  }
}

// Helper function to get current ISO timestamp
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

// Helper function to create contact form entry
export function createContactFormEntry(
  name: string,
  email: string,
  company: string,
  project: string,
  budget: string,
  message: string,
  status: 'success' | 'failed'
): ContactFormEntry {
  return {
    timestamp: getCurrentTimestamp(),
    name: name || '',
    email: email || '',
    company: company || '',
    project_type: project || '',
    budget: budget || '',
    message: message || '',
    submission_status: status
  };
}

// Helper function to create newsletter entry
export function createNewsletterEntry(
  email: string,
  status: 'success' | 'failed'
): NewsletterEntry {
  return {
    timestamp: getCurrentTimestamp(),
    email: email || '',
    subscription_status: status
  };
}
