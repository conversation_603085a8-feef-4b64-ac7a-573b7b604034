import fs from 'fs';
import path from 'path';

// Simple text logging utility for contact forms and newsletter subscriptions

// Debug utility for file paths
function debugFilePath(filePath: string): void {
  console.log('🔍 DEBUG FILE PATH:');
  console.log(`- Full path: ${filePath}`);
  console.log(`- Normalized: ${path.normalize(filePath)}`);
  console.log(`- Parent directory exists: ${fs.existsSync(path.dirname(filePath))}`);
  console.log(`- File exists: ${fs.existsSync(filePath)}`);
}

export interface ContactFormEntry {
  timestamp: string;
  name: string;
  email: string;
  company: string;
  project_type: string;
  budget: string;
  message: string;
  submission_status: 'success' | 'failed';
}

export interface NewsletterEntry {
  timestamp: string;
  email: string;
  subscription_status: 'success' | 'failed';
}

// Format contact form entry as text line
function formatContactFormEntry(entry: ContactFormEntry): string {
  const cleanMessage = entry.message.replace(/\n/g, ' ').replace(/\r/g, ' ').substring(0, 200);
  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;
}

// Format newsletter entry as text line
function formatNewsletterEntry(entry: NewsletterEntry): string {
  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;
}

// Ensure log directory exists
async function ensureLogDirectory(logDir: string): Promise<void> {
  try {
    console.log(`📁 Attempting to create/ensure log directory: ${logDir}`);
    console.log(`📁 Directory exists before mkdir: ${fs.existsSync(logDir)}`);
    
    await fs.promises.mkdir(logDir, { recursive: true });
    
    // Verify directory was created
    const exists = fs.existsSync(logDir);
    console.log(`📁 Log directory ensured: ${logDir} (exists: ${exists})`);
    
    // Check directory permissions
    try {
      const stats = await fs.promises.stat(logDir);
      console.log(`📁 Directory permissions: ${stats.mode.toString(8)}`);
    } catch (statError) {
      console.error(`❌ Could not get directory stats:`, statError);
    }
  } catch (error) {
    console.error(`❌ Error creating log directory ${logDir}:`, error);
    throw error;
  }
}

// Ensure log files exist
export async function ensureLogFiles(): Promise<void> {
  try {
    console.log('🔍 Ensuring log files exist...');
    
    // Get proper server directory path
    const fileURL = new URL(import.meta.url);
    const serverDir = path.dirname(fileURL.pathname);
    const normalizedPath = process.platform === 'win32' 
      ? serverDir.substring(1) 
      : serverDir;
    
    const logDir = path.join(normalizedPath, 'logs');
    const contactFile = path.join(logDir, 'contact-form-submissions.txt');
    const newsletterFile = path.join(logDir, 'newsletter-subscriptions.txt');
    
    // Ensure directory exists
    await ensureLogDirectory(logDir);
    
    // Create files if they don't exist
    for (const file of [contactFile, newsletterFile]) {
      if (!fs.existsSync(file)) {
        console.log(`📄 Creating log file: ${file}`);
        await fs.promises.writeFile(file, '', 'utf8');
      } else {
        console.log(`📄 Log file already exists: ${file}`);
        // Test write access
        try {
          const testPath = file + '.test';
          await fs.promises.writeFile(testPath, 'test', 'utf8');
          await fs.promises.unlink(testPath);
          console.log(`✅ Write access confirmed for: ${file}`);
        } catch (accessError) {
          console.error(`❌ No write access for ${file}:`, accessError);
        }
      }
    }
    
    console.log('✅ Log files ready');
  } catch (error) {
    console.error('❌ Error ensuring log files:', error);
  }
}

// Append data to text file
async function appendToTextFile(filePath: string, data: string): Promise<void> {
  try {
    // Check if the file exists, if not create it
    if (!fs.existsSync(filePath)) {
      console.log(`📁 Creating new log file: ${filePath}`);
      // Ensure the directory structure exists
      const dirPath = path.dirname(filePath);
      if (!fs.existsSync(dirPath)) {
        await fs.promises.mkdir(dirPath, { recursive: true });
        console.log(`📁 Created directory structure: ${dirPath}`);
      }
      await fs.promises.writeFile(filePath, '', 'utf8');
    }
    
    await fs.promises.appendFile(filePath, data + '\n', 'utf8');
    console.log(`📝 Successfully appended to: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error appending to text file ${filePath}:`, error);
    console.error(`File path details - exists: ${fs.existsSync(path.dirname(filePath))}, dir: ${path.dirname(filePath)}`);
    throw error;
  }
}

// Log contact form submission
export async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {
  console.log(`🔍 DEBUG: logContactFormSubmission called for ${entry.email}`);

  let textFile = '';
  let logDir = '';

  try {
    // Fix for ES modules path resolution
    const fileURL = new URL(import.meta.url);
    const serverDir = path.dirname(fileURL.pathname);
    // Remove file:// protocol if present and handle Windows paths
    const normalizedPath = process.platform === 'win32' 
      ? serverDir.substring(1) 
      : serverDir;
    
    logDir = path.join(normalizedPath, 'logs');
    textFile = path.join(logDir, 'contact-form-submissions.txt');

    console.log(`🔍 DEBUG: Log directory: ${logDir}`);
    console.log(`🔍 DEBUG: Text file path: ${textFile}`);
    debugFilePath(textFile);

    // Ensure logs directory exists
    await ensureLogDirectory(logDir);

    // Format entry as text line
    const textLine = formatContactFormEntry(entry);
    console.log(`🔍 DEBUG: Formatted text line: ${textLine}`);

    // Append to file
    await appendToTextFile(textFile, textLine);

    console.log(`✅ Contact form submission logged: ${entry.email} to ${textFile}`);
  } catch (error) {
    console.error('❌ Error logging contact form submission:', error);
    console.error('Text file path:', textFile);
    console.error('Log directory:', logDir);
    // Don't throw error to avoid breaking the main flow
  }
}

// Log newsletter subscription
export async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {
  console.log(`🔍 DEBUG: logNewsletterSubscription called for ${entry.email}`);

  let textFile = '';
  let logDir = '';

  try {
    // Fix for ES modules path resolution
    const fileURL = new URL(import.meta.url);
    const serverDir = path.dirname(fileURL.pathname);
    // Remove file:// protocol if present and handle Windows paths
    const normalizedPath = process.platform === 'win32' 
      ? serverDir.substring(1) 
      : serverDir;
    
    logDir = path.join(normalizedPath, 'logs');
    textFile = path.join(logDir, 'newsletter-subscriptions.txt');

    console.log(`🔍 DEBUG: Log directory: ${logDir}`);
    console.log(`🔍 DEBUG: Text file path: ${textFile}`);
    debugFilePath(textFile);

    // Ensure logs directory exists
    await ensureLogDirectory(logDir);

    // Format entry as text line
    const textLine = formatNewsletterEntry(entry);
    console.log(`🔍 DEBUG: Formatted text line: ${textLine}`);

    // Append to file
    await appendToTextFile(textFile, textLine);

    console.log(`✅ Newsletter subscription logged: ${entry.email} to ${textFile}`);
  } catch (error) {
    console.error('❌ Error logging newsletter subscription:', error);
    console.error('Text file path:', textFile);
    console.error('Log directory:', logDir);
    // Don't throw error to avoid breaking the main flow
  }
}

// Helper function to get current ISO timestamp
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

// Helper function to create contact form entry
export function createContactFormEntry(
  name: string,
  email: string,
  company: string,
  project: string,
  budget: string,
  message: string,
  status: 'success' | 'failed'
): ContactFormEntry {
  console.log(`🔍 DEBUG: createContactFormEntry called for ${email} with status ${status}`);
  return {
    timestamp: getCurrentTimestamp(),
    name: name || '',
    email: email || '',
    company: company || '',
    project_type: project || '',
    budget: budget || '',
    message: message || '',
    submission_status: status
  };
}

// Helper function to create newsletter entry
export function createNewsletterEntry(
  email: string,
  status: 'success' | 'failed'
): NewsletterEntry {
  console.log(`🔍 DEBUG: createNewsletterEntry called for ${email} with status ${status}`);
  return {
    timestamp: getCurrentTimestamp(),
    email: email || '',
    subscription_status: status
  };
}
