{
  "editor.inlineSuggest.enabled": true,
  "workbench.iconTheme": "material-icon-theme",
  "javascript.updateImportsOnFileMove.enabled": "always",
  "git.enableSmartCommit": true,
  "git.autofetch": true,
  "git.confirmSync": false,
  "files.autoSave": "afterDelay",
  "[python]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    },
  },
  "isort.args": ["--profile", "black"],
  "python.linting.mypyEnabled": true,
  "autoDocstring.docstringFormat": "numpy",
  "workbench.editorAssociations": {
    "*.txt": "default",
    "*.py": "default"
  },
  "terminal.integrated.enableMultiLinePasteWarning": false,
  "editor.formatOnSave": true,
  "notebook.formatOnSave.enabled": true,
  "editor.formatOnPaste": true,
"editor.defaultFormatter": "ms-python.black-formatter",
"http.fetchAdditionalSupport": false,
"github.copilot.enable": {
    "*": false
},
"accessibility.signals.terminalBell": {
    "announcement": "off",
    "sound": "off"
},
"chat.agent.enabled": true,
"security.promptForRemoteFileProtocolHandling": false,
"editor.accessibilitySupport": "on",
"accessibility.signals.lineHasBreakpoint": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.chatEditModifiedFile": {
  "sound": "off"
},
"accessibility.signals.chatRequestSent": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.chatResponseReceived": {
  "sound": "off"
},
"accessibility.signals.clear": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.codeActionApplied": {
  "sound": "off"
},
"accessibility.signals.codeActionTriggered": {
  "sound": "off"
},
"accessibility.signals.onDebugBreak": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.diffLineDeleted": {
  "sound": "off"
},
"accessibility.signals.diffLineInserted": {
  "sound": "off"
},
"accessibility.signals.diffLineModified": {
  "sound": "off"
},
"accessibility.signals.editsKept": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.positionHasError": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.lineHasError": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.lineHasFoldedArea": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.lineHasInlineSuggestion": {
  "sound": "off"
},
"accessibility.signals.noInlayHints": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.notebookCellCompleted": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.notebookCellFailed": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.progress": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.taskCompleted": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.taskFailed": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.terminalCommandFailed": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.terminalCommandSucceeded": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.terminalQuickFix": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.editsUndone": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.voiceRecordingStarted": {
  "sound": "off"
},
"accessibility.signals.voiceRecordingStopped": {
  "sound": "off"
},
"accessibility.signals.positionHasWarning": {
  "sound": "off",
  "announcement": "auto"
},
"accessibility.signals.lineHasWarning": {
  "sound": "off",
  "announcement": "auto"
},
"augment.chat.userGuidelines": "I am using VScode on a macbook. Everytime we make changes please make sure my augment-guidelines.md gets updated to reflect the correct way to do things I want to make sure we always maintain a consistent set of standards that we use for everything we do. Please ensure you search the standards before updating so that we can ensure there is no duplication and a clean layout of rules and standards is always maintained.",
"remote.SSH.remotePlatform": {
  "54a32819-81d9-56da-befa-e7e6c1787f2f": "linux"
},
"remote.SSH.defaultExtensions": [],
"remote.SSH.connectTimeout": 15,
"security.workspace.trust.untrustedFiles": "open"
}