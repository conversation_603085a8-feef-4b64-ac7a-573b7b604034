// Email service utilities for form submissions and newsletter

export interface EmailServiceResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface ContactFormSubmission {
  name: string;
  email: string;
  company: string;
  project: string;
  budget: string;
  message: string;
}

export interface NewsletterSubmission {
  email: string;
}

// For production, you would replace this with actual email service integration
// Options include: EmailJS, Formspree, Netlify Forms, or your own backend API

export const submitContactForm = async (data: ContactFormSubmission): Promise<EmailServiceResponse> => {
  try {
    // Send contact form data to our backend API
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      // If MailerSend domain verification fails, provide helpful error message
      if (result.message?.includes('verified domains')) {
        throw new Error('Email service setup required. Please contact us <NAME_EMAIL> while we complete our email configuration.');
      }
      throw new Error(result.message || 'Failed to send message');
    }

    return {
      success: true,
      message: result.message || 'Thank you for your message! We\'ll get back to you within 24 hours.'
    };
  } catch (error) {
    console.error('Contact form submission error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

export const subscribeToNewsletter = async (data: NewsletterSubmission): Promise<EmailServiceResponse> => {
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // For demo purposes, we'll simulate success
    // In production, replace this with actual newsletter service integration
    
    // Example Mailchimp integration:
    // const response = await fetch('/api/newsletter/subscribe', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({ email: data.email }),
    // });

    // Example ConvertKit integration:
    // const response = await fetch('https://api.convertkit.com/v3/forms/YOUR_FORM_ID/subscribe', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({
    //     api_key: 'YOUR_API_KEY',
    //     email: data.email
    //   }),
    // });

    console.log('Newsletter subscription:', data);

    // Simulate occasional failures for testing
    if (Math.random() < 0.05) {
      throw new Error('Subscription service temporarily unavailable');
    }

    return {
      success: true,
      message: 'Successfully subscribed! Check your email for confirmation.'
    };
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return {
      success: false,
      message: 'Sorry, there was an error with your subscription. Please try again.',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
