// Email service utilities for form submissions and newsletter

export interface EmailServiceResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface ContactFormSubmission {
  name: string;
  email: string;
  company: string;
  project: string;
  budget: string;
  message: string;
}

export interface NewsletterSubmission {
  email: string;
}

// For production, you would replace this with actual email service integration
// Options include: EmailJS, Formspree, Netlify Forms, or your own backend API

export const submitContactForm = async (data: ContactFormSubmission): Promise<EmailServiceResponse> => {
  try {
    // Send contact form data to our backend API
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      // If MailerSend domain verification fails, provide helpful error message
      if (result.message?.includes('verified domains')) {
        throw new Error('Email service setup required. Please contact us <NAME_EMAIL> while we complete our email configuration.');
      }
      throw new Error(result.message || 'Failed to send message');
    }

    return {
      success: true,
      message: result.message || 'Thank you for your message! We\'ll get back to you within 24 hours.'
    };
  } catch (error) {
    console.error('Contact form submission error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

export const subscribeToNewsletter = async (data: NewsletterSubmission): Promise<EmailServiceResponse> => {
  try {
    // Send newsletter subscription to our backend API
    const response = await fetch('/api/newsletter', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to subscribe to newsletter');
    }

    return {
      success: true,
      message: result.message || 'Successfully subscribed! Check your email for confirmation.'
    };
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Sorry, there was an error with your subscription. Please try again.',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
