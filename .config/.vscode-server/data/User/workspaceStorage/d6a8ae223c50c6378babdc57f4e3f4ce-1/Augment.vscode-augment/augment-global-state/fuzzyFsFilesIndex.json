{"/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}, "/home/<USER>/workspace/.replit": {"rootPath": "/home/<USER>/workspace", "relPath": ".replit"}, "/home/<USER>/workspace/components.json": {"rootPath": "/home/<USER>/workspace", "relPath": "components.json"}, "/home/<USER>/workspace/drizzle.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "drizzle.config.ts"}, "/home/<USER>/workspace/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package.json"}, "/home/<USER>/workspace/postcss.config.js": {"rootPath": "/home/<USER>/workspace", "relPath": "postcss.config.js"}, "/home/<USER>/workspace/tailwind.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "tailwind.config.ts"}, "/home/<USER>/workspace/tsconfig.json": {"rootPath": "/home/<USER>/workspace", "relPath": "tsconfig.json"}, "/home/<USER>/workspace/vite.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "vite.config.ts"}, "/home/<USER>/workspace/client/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": "client/index.html"}, "/home/<USER>/workspace/client/src/vite-env.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/vite-env.d.ts"}, "/home/<USER>/workspace/client/src/components/Footer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Footer.tsx"}, "/home/<USER>/workspace/.local/state/replit/agent/.latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/.latest.json"}, "/home/<USER>/workspace/.local/state/replit/agent/rapid_build_success": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/rapid_build_success"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/NOTICE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/NOTICE.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/syntaxes/ref.tmGrammar.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/syntaxes/ref.tmGrammar.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/assets/status/documentation.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/assets/status/documentation.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/package.json"}, "/home/<USER>/workspace/.cache/replit/modules.stamp": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules.stamp"}, "/home/<USER>/workspace/.cache/replit/nix/dotreplitenv.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/nix/dotreplitenv.json"}, "/home/<USER>/workspace/.cache/replit/modules/nodejs-20.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/nodejs-20.res"}, "/home/<USER>/workspace/.cache/replit/modules/postgresql-16.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/postgresql-16.res"}, "/home/<USER>/workspace/.cache/replit/modules/replit.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/replit.res"}, "/home/<USER>/workspace/.cache/replit/modules/web.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/web.res"}, "/home/<USER>/workspace/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".giti<PERSON>re"}, "/home/<USER>/workspace/augment-guidelines.md": {"rootPath": "/home/<USER>/workspace", "relPath": "augment-guidelines.md"}, "/home/<USER>/workspace/replit.md": {"rootPath": "/home/<USER>/workspace", "relPath": "replit.md"}, "/home/<USER>/workspace/test-email.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-email.js"}, "/home/<USER>/workspace/shared/schema.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/schema.ts"}, "/home/<USER>/workspace/server/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}, "/home/<USER>/workspace/server/routes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}, "/home/<USER>/workspace/server/storage.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/storage.ts"}, "/home/<USER>/workspace/server/vite.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/vite.ts"}, "/home/<USER>/workspace/client/src/App.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/App.tsx"}, "/home/<USER>/workspace/client/src/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/index.css"}, "/home/<USER>/workspace/client/src/main.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/main.tsx"}, "/home/<USER>/workspace/client/src/utils/emailService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/emailService.ts"}, "/home/<USER>/workspace/client/src/utils/navigation.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/navigation.ts"}, "/home/<USER>/workspace/client/src/utils/validation.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/validation.ts"}, "/home/<USER>/workspace/client/src/types/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/types/index.ts"}, "/home/<USER>/workspace/client/src/hooks/useForm.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useForm.ts"}, "/home/<USER>/workspace/client/src/hooks/useTheme.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useTheme.tsx"}, "/home/<USER>/workspace/client/src/components/Contact.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Contact.tsx"}, "/home/<USER>/workspace/client/src/components/Header.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Header.tsx"}, "/home/<USER>/workspace/client/src/components/Hero.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Hero.tsx"}, "/home/<USER>/workspace/client/src/components/Portfolio.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Portfolio.tsx"}, "/home/<USER>/workspace/client/src/components/Services.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Services.tsx"}, "/home/<USER>/workspace/client/src/components/TechStack.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/TechStack.tsx"}, "/home/<USER>/workspace/client/src/components/Testimonials.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Testimonials.tsx"}, "/home/<USER>/workspace/.upm/store.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".upm/store.json"}, "/home/<USER>/workspace/.local/state/replit/agent/progress_tracker.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/progress_tracker.md"}, "/home/<USER>/workspace/.config/.vscode-server/.cli.dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/.cli.dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/extensions.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/extensions.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.nls.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/resources/icons/pr_webview.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/resources/icons/pr_webview.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/resources/icons/dark/pr_webview.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/resources/icons/dark/pr_webview.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/changelog.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/changelog.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.it.json"}, "/home/<USER>/workspace/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package-lock.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/changelog.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/changelog.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/bundle.l10n.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/bing.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/bing.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/debug-icon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/debug-icon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/vscode-chat-avatar-insiders.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/vscode-chat-avatar-insiders.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/vscode-chat-avatar-stable.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/vscode-chat-avatar-stable.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/CHANGELOG.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/CHANGELOG.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/activitybar.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/activitybar.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/panel-icon-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/panel-icon-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/panel-icon-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/panel-icon-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-applied-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-applied-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-applied-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-applied-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-available-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-available-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-available-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-available-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-change-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-change-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-change-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-change-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-change-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-change-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-change-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-change-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-deletion-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-deletion-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-deletion-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-deletion-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-deletion-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-deletion-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-deletion-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-deletion-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-rejected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-rejected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-rejected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-rejected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-unavailable-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-unavailable-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-unavailable-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-unavailable-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-complete-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-complete-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-complete-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-complete-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-disabled-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-disabled-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-disabled-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-disabled-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-update-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/right-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/right-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/right-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/right-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/right-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/right-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/right-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/right-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/autofix.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/autofix.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/diff-view.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/diff-view.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/history.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/history.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/main-panel.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/main-panel.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/memories.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/memories.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/next-edit-suggestions.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/next-edit-suggestions.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/preference.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/preference.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/remote-agent-diff.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/remote-agent-diff.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/remote-agent-home.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/remote-agent-home.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/rules.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/rules.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/settings.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/settings.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/AugmentMessage-NADO4oNJ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/AugmentMessage-NADO4oNJ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/BaseButton-BriUAFwy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/BaseButton-BriUAFwy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/BaseButton-DvMdfQ3F.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/BaseButton-DvMdfQ3F.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ButtonAugment-C98iBH51.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ButtonAugment-C98iBH51.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ButtonAugment-CNK8zC8i.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ButtonAugment-CNK8zC8i.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CalloutAugment-BFrX0piu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CalloutAugment-BFrX0piu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CalloutAugment-Dvw-pMXw.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CalloutAugment-Dvw-pMXw.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CardAugment-BAo8Ti0V.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CardAugment-BAo8Ti0V.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CardAugment-QSBW3w4A.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CardAugment-QSBW3w4A.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CollapseButtonAugment-BKGRuCNC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CollapseButtonAugment-BKGRuCNC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CollapseButtonAugment-YENylnyN.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CollapseButtonAugment-YENylnyN.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Filespan-D-BqE8vd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Filespan-D-BqE8vd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Filespan-tclW2Ian.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Filespan-tclW2Ian.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/IconButtonAugment-BTu-iglL.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/IconButtonAugment-BTu-iglL.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/IconButtonAugment-C_49EbFE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/IconButtonAugment-C_49EbFE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/IconFilePath-CiKel2Kp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/IconFilePath-CiKel2Kp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/IconFilePath-D5Jg9jcC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/IconFilePath-D5Jg9jcC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Keybindings-BFFBoxX3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Keybindings-BFFBoxX3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/LanguageIcon-D78BqCXT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/LanguageIcon-D78BqCXT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/LanguageIcon-FVMxq7uD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/LanguageIcon-FVMxq7uD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MarkdownEditor-DgKEQL-6.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MarkdownEditor-DgKEQL-6.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MarkdownEditor-zNvUkrOp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MarkdownEditor-zNvUkrOp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MaterialIcon-8-Z76Y2_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MaterialIcon-8-Z76Y2_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MaterialIcon-BO_oU5T3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MaterialIcon-BO_oU5T3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MessageList-DRTeF5X0.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MessageList-DRTeF5X0.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/NextEditSuggestions-CowAk1jX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/NextEditSuggestions-CowAk1jX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/OpenFileButton-BAZcfB84.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/OpenFileButton-BAZcfB84.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/OpenFileButton-BO1gXf_-.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/OpenFileButton-BO1gXf_-.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RemoteAgentRetry-BjJDbKtf.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RemoteAgentRetry-BjJDbKtf.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/arc-ByXcqKcW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/arc-ByXcqKcW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/await_block-H61A9-v_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/await_block-H61A9-v_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/bat-CtWuqYvB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/bat-CtWuqYvB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/design-system-init-CrDNmo5Z.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/design-system-init-CrDNmo5Z.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/design-system-init-y6tm-B4G.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/design-system-init-y6tm-B4G.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diagram-QW4FP2JN-Bs4EyU8I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diagram-QW4FP2JN-Bs4EyU8I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diff-utils-DTcQ2vsq.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diff-utils-DTcQ2vsq.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dockerfile-DLk6rpji.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dockerfile-DLk6rpji.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ecl-BO6FnfXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ecl-BO6FnfXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ecl-DrG4DZS2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ecl-DrG4DZS2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/elixir-BRjLKONM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/elixir-BRjLKONM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/elixir-nOQiPlLZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/elixir-nOQiPlLZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ellipsis-Cm0UKVWz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ellipsis-Cm0UKVWz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/exclamation-triangle-BbVpV4C-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/exclamation-triangle-BbVpV4C-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/expand-CURYX9ur.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/expand-CURYX9ur.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/file-paths-BcSg4gks.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/file-paths-BcSg4gks.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/file-reader-BZAZY_XQ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/file-reader-BZAZY_XQ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/package.nls.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-gray-hook.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-gray-hook.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-gray-line.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-gray-line.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-inactive-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-inactive-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-inactive-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-inactive-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/bg-next-edit-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/left-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/left-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/left-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/left-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/left-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/left-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/left-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/left-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-inbetween-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-inbetween-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-inbetween-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-inbetween-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/nextedit-addition-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Content-BiWRcmeV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Content-BiWRcmeV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Content-LuLOeTld.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Content-LuLOeTld.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CopyButton-C581jDHd.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CopyButton-C581jDHd.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CopyButton-V5A3ZeY-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/CopyButton-V5A3ZeY-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Drawer-D1zHZTq7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Drawer-D1zHZTq7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Drawer-DwFbLE28.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Drawer-DwFbLE28.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RemoteAgentRetry-Ddih_AAu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RemoteAgentRetry-Ddih_AAu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RemoteAgentSetup-CE-yp3a6.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RemoteAgentSetup-CE-yp3a6.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RemoteAgentSetup-Dh9zk49a.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RemoteAgentSetup-Dh9zk49a.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RulesDropdown-BIuf9YLm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/RulesDropdown-BIuf9YLm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/SpinnerAugment-Cx9dt_ox.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/SpinnerAugment-Cx9dt_ox.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/SpinnerAugment-DnPofOlT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/SpinnerAugment-DnPofOlT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/StatusIndicator-D-yOSWp9.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/StatusIndicator-D-yOSWp9.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/StatusIndicator-DbaBoBGY.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/StatusIndicator-DbaBoBGY.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/TextAreaAugment-BfXODIdS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/TextAreaAugment-BfXODIdS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/TextAreaAugment-J75lFxU7.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/TextAreaAugment-J75lFxU7.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/TextTooltipAugment-CXnRMJBa.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/TextTooltipAugment-CXnRMJBa.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/TextTooltipAugment-s8TDgDBs.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/TextTooltipAugment-s8TDgDBs.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/VSCodeCodicon-B3px2_jp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/VSCodeCodicon-B3px2_jp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/VSCodeCodicon-DVaocTud.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/VSCodeCodicon-DVaocTud.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/VirtualizedMessageList-DuPzljP2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/VirtualizedMessageList-DuPzljP2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/_basePickBy-BL_7Z--h.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/_basePickBy-BL_7Z--h.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/_baseUniq-DeZOa7to.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/_baseUniq-DeZOa7to.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/abap-BrlRCFwh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/abap-BrlRCFwh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/abap-CRCWOmpq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/abap-CRCWOmpq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/apex-BE2Kqs_0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/apex-BE2Kqs_0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/apex-DFVco9Dq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/apex-DFVco9Dq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/architectureDiagram-UYN6MBPD-D5kO_l-g.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/architectureDiagram-UYN6MBPD-D5kO_l-g.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/arrow-up-right-from-square-D0fwOso7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/arrow-up-right-from-square-D0fwOso7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/autofix-state-d-ymFdyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/autofix-state-d-ymFdyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/autofix-zYG0aVGy.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/autofix-zYG0aVGy.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/azcli-1IWB1ccx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/azcli-1IWB1ccx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/bat-DPkNLes8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/bat-DPkNLes8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/bicep-BZbtZWRn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/bicep-BZbtZWRn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/blockDiagram-ZHA2E4KO-D49BD2tk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/blockDiagram-ZHA2E4KO-D49BD2tk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/c4Diagram-6F5ED5ID-KX62wGaW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/c4Diagram-6F5ED5ID-KX62wGaW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cameligo-CGrWLZr3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cameligo-CGrWLZr3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cameligo-hfF0gFWA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cameligo-hfF0gFWA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/channel-Baa_Wiox.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/channel-Baa_Wiox.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chat-types-B-te1sXh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chat-types-B-te1sXh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/clojure-BhAVYYK7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/clojure-BhAVYYK7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/clojure-D9WOWImG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/clojure-D9WOWImG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/clone-B5cJ26T2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/clone-B5cJ26T2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/coffee-B7EJu28W.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/coffee-B7EJu28W.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/coffee-D3gVwdtb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/coffee-D3gVwdtb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cpp-B6k-yq-r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cpp-B6k-yq-r.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cpp-DghbrAFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cpp-DghbrAFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/csharp-1bC6NAu3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/csharp-1bC6NAu3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/csharp-BoL64M5l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/csharp-BoL64M5l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/csp-C46ZqvIl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/csp-C46ZqvIl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/csp-ZI2qu8Le.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/csp-ZI2qu8Le.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/css-BkD51DMU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/css-BkD51DMU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/css-DQU6DXDx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/css-DQU6DXDx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cssMode-BBxcXVId.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cssMode-BBxcXVId.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cssMode-Cf0wo1J6.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cssMode-Cf0wo1J6.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cypher-D84EuPTj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cypher-D84EuPTj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cypher-DQ3GyGCv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cypher-DQ3GyGCv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/freemarker2-DoNuTueB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/freemarker2-DoNuTueB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/fsharp-BpBzFqoi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/fsharp-BpBzFqoi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/fsharp-fd1GTHhf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/fsharp-fd1GTHhf.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/hcl-CVzGlmMO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/hcl-CVzGlmMO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pgsql-Dy0bjov7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pgsql-Dy0bjov7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mysql-CMGNIvT0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mysql-CMGNIvT0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scheme-Ecrf_Zyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scheme-Ecrf_Zyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778789715-51d3924b-d0df-4a1c-bfc4-cb66242af6c4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778789715-51d3924b-d0df-4a1c-bfc4-cb66242af6c4.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778525069-38569d4f-7e59-4b60-9e5f-f4fa2dccdf38.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778525069-38569d4f-7e59-4b60-9e5f-f4fa2dccdf38.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/24165b42/BjnT.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/24165b42/BjnT.ts"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/keypress-DD1aQVr0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/keypress-DD1aQVr0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/kotlin-qQ0MG-9I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/kotlin-qQ0MG-9I.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.copilot-chat/GitHub Copilot Chat.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.copilot-chat/GitHub Copilot Chat.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/terminal-BjJSzToG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/terminal-BjJSzToG.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/settings-Dxjpthh-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/settings-Dxjpthh-.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/python-DY2G-JB8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/python-DY2G-JB8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/yaml-Cr3uXDXT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/yaml-Cr3uXDXT.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778498883-226f6565-85ef-4f6e-983e-b3843f019dff.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778498883-226f6565-85ef-4f6e-983e-b3843f019dff.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/globals-D0QH3NT1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/globals-D0QH3NT1.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/shell-CNhb_Zkf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/shell-CNhb_Zkf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ganttDiagram-NTVNEXSI-Sav4zqWF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ganttDiagram-NTVNEXSI-Sav4zqWF.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778832664-093291f3-3c70-4a97-ae15-21fe0acc641c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778832664-093291f3-3c70-4a97-ae15-21fe0acc641c.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/isObjectLike-Bk6im6rM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/isObjectLike-Bk6im6rM.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749780227664-0b0cb961-59c2-46de-b6cc-86caeba32065.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749780227664-0b0cb961-59c2-46de-b6cc-86caeba32065.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778665224-f7b72794-e04f-4a4c-81d4-e87510144dcb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778665224-f7b72794-e04f-4a4c-81d4-e87510144dcb.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/428efd6e/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/428efd6e/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/pid.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/pid.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/javascript-BM_VUh7x.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/javascript-BM_VUh7x.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/remote-agent-diff-ALf3wJBM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/remote-agent-diff-ALf3wJBM.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778541392-f09ee82c-0fa8-4711-8656-a449284b67f3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778541392-f09ee82c-0fa8-4711-8656-a449284b67f3.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/redis-CHOsPHWR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/redis-CHOsPHWR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/typescript-DTP-A_Zf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/typescript-DTP-A_Zf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-T2TOU4HS-B8XZT5R3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-T2TOU4HS-B8XZT5R3.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.copilot-chat/GitHub Copilot Chat.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.copilot-chat/GitHub Copilot Chat.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/qsharp-q7JyzKFN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/qsharp-q7JyzKFN.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-327b1cf9/6HAY.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-327b1cf9/6HAY.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/4fa79aeb-a20a-45c7-97df-01eb941a5c99": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/4fa79aeb-a20a-45c7-97df-01eb941a5c99"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pen-to-square-CZwCjcp0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pen-to-square-CZwCjcp0.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/product.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/product.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/images/code.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/images/code.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/liquid-DOEm5dbE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/liquid-DOEm5dbE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mips-Cu7FWeYr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mips-Cu7FWeYr.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5ddef898/R0IY.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5ddef898/R0IY.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_navigation.ts-1749778397511-cde24eab-9da8-46dc-a72f-1f99d074c62a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_navigation.ts-1749778397511-cde24eab-9da8-46dc-a72f-1f99d074c62a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-53ad80eb/UMwT.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-53ad80eb/UMwT.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_emailService.ts-1749778385599-2792f5c6-e244-4057-afcf-9856997726b5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_emailService.ts-1749778385599-2792f5c6-e244-4057-afcf-9856997726b5.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sankeyDiagram-Y46BX6SQ-Cf4d-6Pq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sankeyDiagram-Y46BX6SQ-Cf4d-6Pq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-327b1cf9/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-327b1cf9/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778774876-446afc77-b414-4bc4-ac3a-9083792f3b56.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778774876-446afc77-b414-4bc4-ac3a-9083792f3b56.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/HJMu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/HJMu.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/jsonMode-B6_b0_sN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/jsonMode-B6_b0_sN.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778818375-437d4de0-8f44-475d-a077-c339b6d4d627.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778818375-437d4de0-8f44-475d-a077-c339b6d4d627.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lua-BdjVVLHC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lua-BdjVVLHC.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/r-BIFz-_sK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/r-BIFz-_sK.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5980142/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5980142/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/next-edit-types-904A5ehg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/next-edit-types-904A5ehg.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/copilot-debug": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/copilot-debug"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/D4CY.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/D4CY.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pascal-BhNW15KB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pascal-BhNW15KB.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778136758-d064627d-4f4d-43b7-b7cd-6f25880ee89e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778136758-d064627d-4f4d-43b7-b7cd-6f25880ee89e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/tcl-Bl2hYPt-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/tcl-Bl2hYPt-.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.typescript-language-features/TypeScript.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.typescript-language-features/TypeScript.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/tsMode-BXDgwTrJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/tsMode-BXDgwTrJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/1CTQ.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/1CTQ.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-DUiNNixO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-DUiNNixO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/perl-DlYyT36c.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/perl-DlYyT36c.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/java-DBwYS35M.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/java-DBwYS35M.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778789524-76769c2f-4bc7-440a-9d19-a9595219186f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778789524-76769c2f-4bc7-440a-9d19-a9595219186f.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/toggleHighContrast-D4zjdeIP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/toggleHighContrast-D4zjdeIP.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/freemarker2-CG76HvIH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/freemarker2-CG76HvIH.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_emailService.ts-1749780139665-4e75f5f6-afcc-49ec-a48a-6e07a54be5e4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_emailService.ts-1749780139665-4e75f5f6-afcc-49ec-a48a-6e07a54be5e4.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778605223-88652d8a-f89e-408c-a78d-d1390ae61446.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778605223-88652d8a-f89e-408c-a78d-d1390ae61446.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7d44b9c4/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7d44b9c4/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/q64o.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/q64o.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/dp07.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/dp07.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778863715-e43b2fcf-ff1d-4665-8145-8c1ac8c25bcd.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778863715-e43b2fcf-ff1d-4665-8145-8c1ac8c25bcd.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778619629-e2f60807-eb83-4eab-af66-69835abb15f2.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778619629-e2f60807-eb83-4eab-af66-69835abb15f2.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/handlebars-BJSeNQ27.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/handlebars-BJSeNQ27.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/solidity-CME5AdoB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/solidity-CME5AdoB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/systemverilog-CeZ7LPTL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/systemverilog-CeZ7LPTL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dockerfile-CuMHdPl5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dockerfile-CuMHdPl5.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778604994-0dfb5b90-c1c0-4dd9-9138-100e6cb3c767.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778604994-0dfb5b90-c1c0-4dd9-9138-100e6cb3c767.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778832079-6906b26a-1617-451d-bd9e-d1bfe1498e1f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778832079-6906b26a-1617-451d-bd9e-d1bfe1498e1f.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/layout-D9KQFTZ-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/layout-D9KQFTZ-.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.typescript-language-features/TypeScript.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.typescript-language-features/TypeScript.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/katex-BAVf198l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/katex-BAVf198l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/linear-Ba_CriMz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/linear-Ba_CriMz.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2752f7d5/5GJo": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2752f7d5/5GJo"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778804300-5c53c15a-8230-483c-9d12-3a4162c09186.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778804300-5c53c15a-8230-483c-9d12-3a4162c09186.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778573960-43f2ff60-d149-42cd-9141-27215ce31d1a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778573960-43f2ff60-d149-42cd-9141-27215ce31d1a.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778591624-272954cf-c06c-4637-915d-f2f9f8624e59.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778591624-272954cf-c06c-4637-915d-f2f9f8624e59.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pug-kFxLfcjb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pug-kFxLfcjb.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778541597-f57ea05d-73fd-4932-baee-8e808b93ec25.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778541597-f57ea05d-73fd-4932-baee-8e808b93ec25.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pla-B-trYkKT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pla-B-trYkKT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/github-BAG1lC_i.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/github-BAG1lC_i.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778711773-607760ce-9eca-4f1c-85f6-d8afcfb77769.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778711773-607760ce-9eca-4f1c-85f6-d8afcfb77769.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dart-CQal6Qht.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dart-CQal6Qht.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-0-f01fa154-485a-4a86-9187-ebb8ec417b13.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-0-f01fa154-485a-4a86-9187-ebb8ec417b13.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/lru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/lru.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/markdown-B811l8j2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/markdown-B811l8j2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/protobuf-BQ74DTcm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/protobuf-BQ74DTcm.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MessageList-Bl0qLm3e.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/MessageList-Bl0qLm3e.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scss-CTwUZ5N7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scss-CTwUZ5N7.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778804300-d71ace83-d64c-489c-8b0e-728f3624a182.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778804300-d71ace83-d64c-489c-8b0e-728f3624a182.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778591624-eefdf95e-dc86-4ecd-a59a-d0bb797e8f52.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778591624-eefdf95e-dc86-4ecd-a59a-d0bb797e8f52.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-5HRBRIJM-D37Rv7DX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-5HRBRIJM-D37Rv7DX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/remote-agent-home-FEmWbk3M.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/remote-agent-home-FEmWbk3M.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778483904-30bb4ad5-e91f-48a3-9b17-42452d369a7d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778483904-30bb4ad5-e91f-48a3-9b17-42452d369a7d.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/428efd6e/MbRl.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/428efd6e/MbRl.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_vite.ts-0-c653332e-c5cf-40a6-8be3-226961549305.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_vite.ts-0-c653332e-c5cf-40a6-8be3-226961549305.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/open-in-new-window-BnOaIUsp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/open-in-new-window-BnOaIUsp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/julia-DQXNmw_w.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/julia-DQXNmw_w.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/copilotDebugCommand.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/copilotDebugCommand.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/flow9-DFOiqFq1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/flow9-DFOiqFq1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/postiats-CQpG440k.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/postiats-CQpG440k.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_hooks_useForm.ts-1749778416399-9b589198-1255-46ba-8ac3-1bd0cf576cbb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_hooks_useForm.ts-1749778416399-9b589198-1255-46ba-8ac3-1bd0cf576cbb.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778845879-5a0024be-746e-4095-bc21-c0d55a4ca48a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778845879-5a0024be-746e-4095-bc21-c0d55a4ca48a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778202623-5017bf6c-b699-438b-bc3c-9763fee34a84.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778202623-5017bf6c-b699-438b-bc3c-9763fee34a84.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mips-CdjsipkG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mips-CdjsipkG.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.css-language-features/CSS Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.css-language-features/CSS Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/qsharp-YKUDF0Oj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/qsharp-YKUDF0Oj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/st-BZ7aq21L.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/st-BZ7aq21L.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778151112-3525abd9-34b1-44d2-8932-cd6610f16ef9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778151112-3525abd9-34b1-44d2-8932-cd6610f16ef9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778573960-2380d4fa-a93c-4c62-ab82-043cf484419d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778573960-2380d4fa-a93c-4c62-ab82-043cf484419d.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ruby-CYWGW-b1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ruby-CYWGW-b1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/powerquery-DdJtto1Z.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/powerquery-DdJtto1Z.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-0-fd4344aa-c46c-4d55-b9d9-ad385203a87f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-0-fd4344aa-c46c-4d55-b9d9-ad385203a87f.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-ASOPGD6M-B0SkWinB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-ASOPGD6M-B0SkWinB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scala-Bqvq8jcR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scala-Bqvq8jcR.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778189523-a2c12b03-c0fb-43e3-bcd3-aec4aa8010df.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778189523-a2c12b03-c0fb-43e3-bcd3-aec4aa8010df.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/83703af2-93b9-4d23-800c-9047a2c44bc8": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/83703af2-93b9-4d23-800c-9047a2c44bc8"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778498687-5084eb01-9c67-4d60-97f6-3d4d7fc94ba5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778498687-5084eb01-9c67-4d60-97f6-3d4d7fc94ba5.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chevron-down-DYf4hfS2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chevron-down-DYf4hfS2.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778426912-12792578-15b6-470d-bf33-8183ca9cd93b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778426912-12792578-15b6-470d-bf33-8183ca9cd93b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_index.ts-1749780207493-9b063393-dfe4-411f-bc86-138a0fca9ddf.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_index.ts-1749780207493-9b063393-dfe4-411f-bc86-138a0fca9ddf.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778679073-cb9899fa-91e7-47b0-997a-6780adfe3333.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778679073-cb9899fa-91e7-47b0-997a-6780adfe3333.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diff-view-DmXmqa2M.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diff-view-DmXmqa2M.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/mvwW.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/mvwW.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778711773-85b19799-d899-42d2-918e-2484d633a121.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778711773-85b19799-d899-42d2-918e-2484d633a121.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-45e5f64c/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-45e5f64c/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/jsonMode-Dek7wPyh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/jsonMode-Dek7wPyh.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ini-BvajGCUy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ini-BvajGCUy.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778679288-f7a9e8ad-5a46-426b-afb3-c260869500c3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778679288-f7a9e8ad-5a46-426b-afb3-c260869500c3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778136758-44faf24b-3747-4383-83ac-240d530742de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778136758-44faf24b-3747-4383-83ac-240d530742de.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7d44b9c4/SYi7.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7d44b9c4/SYi7.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778651146-573a7af9-61e9-462a-8cb9-b2ad554e0ccb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778651146-573a7af9-61e9-462a-8cb9-b2ad554e0ccb.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/typespec-DKGjpBXL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/typespec-DKGjpBXL.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778693967-854795d4-4c6a-4899-84ba-5e068837e3c5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778693967-854795d4-4c6a-4899-84ba-5e068837e3c5.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778759854-fe3e79c6-d156-433b-97cf-2f5eb5343881.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778759854-fe3e79c6-d156-433b-97cf-2f5eb5343881.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778892731-abc0fd8a-4dc5-46ce-9d10-60f589558944.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778892731-abc0fd8a-4dc5-46ce-9d10-60f589558944.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Keybindings-CmKZYlFV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/Keybindings-CmKZYlFV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ordinal-_rw2EY4v.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ordinal-_rw2EY4v.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778525272-741925ae-da22-428a-8136-4c5683475116.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778525272-741925ae-da22-428a-8136-4c5683475116.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sql-BV61QDTH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sql-BV61QDTH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/php-C92L-r_Y.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/php-C92L-r_Y.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780311962-01cd3ef6-361a-413c-b981-45bfa30b64a4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780311962-01cd3ef6-361a-413c-b981-45bfa30b64a4.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/Hk5C.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/Hk5C.ts"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/java-SYsfObOQ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/java-SYsfObOQ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/liquid-CPIgs5dT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/liquid-CPIgs5dT.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/bicep-C6yweCii.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/bicep-C6yweCii.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/resize-observer-DdAtcrRr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/resize-observer-DdAtcrRr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/postiats-ToQhlN1R.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/postiats-ToQhlN1R.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5980142/5sfM.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5980142/5sfM.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/T5PE.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/T5PE.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.copilot-chat/GitHub Copilot Chat.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.copilot-chat/GitHub Copilot Chat.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780311962-b899334d-736c-407b-aa6d-4a52cc262a8a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780311962-b899334d-736c-407b-aa6d-4a52cc262a8a.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/powerquery-BLkMU_zt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/powerquery-BLkMU_zt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/go-CHYgS3dC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/go-CHYgS3dC.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_index.ts-1749780208066-e6794367-7708-410c-a0bb-1834e91a5049.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_index.ts-1749780208066-e6794367-7708-410c-a0bb-1834e91a5049.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778443080-d25eb156-c724-49f0-8529-dd568c516a6a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778443080-d25eb156-c724-49f0-8529-dd568c516a6a.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/st-C7iG7M4S.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/st-C7iG7M4S.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2752f7d5/5CRY": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2752f7d5/5CRY"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/qDQV.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/qDQV.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dagre-4EVJKHTY-BQCvARMp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dagre-4EVJKHTY-BQCvARMp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/graph-DcRYZ40r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/graph-DcRYZ40r.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5ddef898/Pbcv.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5ddef898/Pbcv.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/typespec-5IKh-a8s.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/typespec-5IKh-a8s.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/stateDiagram-MAYHULR4-B-3qXIt8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/stateDiagram-MAYHULR4-B-3qXIt8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/swift-DqwpnxQL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/swift-DqwpnxQL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pascaligo-5jv8CcQD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pascaligo-5jv8CcQD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/memories-Dnh1QlsF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/memories-Dnh1QlsF.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778651146-0fb91fcf-9f6e-4184-b638-2629fd450781.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778651146-0fb91fcf-9f6e-4184-b638-2629fd450781.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scss-DuQSCaUL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scss-DuQSCaUL.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778443080-b593e696-11fe-4297-8aac-1bdb65fc1977.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778443080-b593e696-11fe-4297-8aac-1bdb65fc1977.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778846079-788e012b-aa6d-4c1c-bb3c-97e3ec51adf0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778846079-788e012b-aa6d-4c1c-bb3c-97e3ec51adf0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-0-1666e020-1c68-4d97-b233-6b98a79d7815.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-0-1666e020-1c68-4d97-b233-6b98a79d7815.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/infoDiagram-A4XQUW5V-DGG-C3CO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/infoDiagram-A4XQUW5V-DGG-C3CO.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/jsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/jsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/types-Cgd-nZOV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/types-Cgd-nZOV.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778573755-7085cdaf-9d65-47b9-9181-1fe84eae2f55.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778573755-7085cdaf-9d65-47b9-9181-1fe84eae2f55.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/history-BjjIMjo4.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/history-BjjIMjo4.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/wgsl-Du36xR5C.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/wgsl-Du36xR5C.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778665424-d5c78f14-5658-4024-ad34-5724236120f6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778665424-d5c78f14-5658-4024-ad34-5724236120f6.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-53ad80eb/E71n.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-53ad80eb/E71n.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/file-reader-BoOyi4LU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/file-reader-BoOyi4LU.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778892482-2cfeda08-af8c-4582-95e3-ec3df3786dfb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778892482-2cfeda08-af8c-4582-95e3-ec3df3786dfb.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/twig-h6VuAx0U.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/twig-h6VuAx0U.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778483904-bd61791b-4c7b-4ba0-9ee2-214b632e8c7d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778483904-bd61791b-4c7b-4ba0-9ee2-214b632e8c7d.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.gitignore-1749780181049-*************-4b47-aeab-6e819ae80ed5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.gitignore-1749780181049-*************-4b47-aeab-6e819ae80ed5.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778892731-356abf7e-53cf-4520-bca1-802d7dd21d00.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778892731-356abf7e-53cf-4520-bca1-802d7dd21d00.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/less-GGFNNJHn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/less-GGFNNJHn.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778639138-fdc41615-e049-4a09-8a3c-becffc20ebfe.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778639138-fdc41615-e049-4a09-8a3c-becffc20ebfe.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.vscode-pull-request-github/GitHub Pull Request.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.vscode-pull-request-github/GitHub Pull Request.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778846079-9da06c85-5979-457f-b718-2ce4a9495478.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778846079-9da06c85-5979-457f-b718-2ce4a9495478.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.copilot/GitHub Copilot.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.copilot/GitHub Copilot.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/msdax-DBX3bZkL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/msdax-DBX3bZkL.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/output_logging_20250613T021533/1-GitHub Copilot Log (Code References).log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/output_logging_20250613T021533/1-GitHub Copilot Log (Code References).log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778638873-928bc64a-179a-48fa-9862-27557db40555.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778638873-928bc64a-179a-48fa-9862-27557db40555.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/tcl-PloMZuKG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/tcl-PloMZuKG.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_index.ts-0-a8de28a2-2ff8-417c-8581-dcc164de4b3a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_index.ts-0-a8de28a2-2ff8-417c-8581-dcc164de4b3a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7d44b9c4/rUZ8.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7d44b9c4/rUZ8.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/html-gnlaprsJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/html-gnlaprsJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/rust-APfvjYow.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/rust-APfvjYow.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mysql-BHd6q0vd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mysql-BHd6q0vd.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-0-fdce17d8-469b-46a1-ad67-9f4debfb176f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-0-fdce17d8-469b-46a1-ad67-9f4debfb176f.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/powershell-Bu_VLpJB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/powershell-Bu_VLpJB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/quadrantDiagram-OS5C2QUG-CxLFlXBH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/quadrantDiagram-OS5C2QUG-CxLFlXBH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/m3-B3V054Zg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/m3-B3V054Zg.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.vscode-pull-request-github/GitHub Pull Request.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.vscode-pull-request-github/GitHub Pull Request.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/utils-BjvAGb_o.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/utils-BjvAGb_o.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778427512-e13f374c-1c3a-47e6-9511-89161e3c549f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778427512-e13f374c-1c3a-47e6-9511-89161e3c549f.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/folder-VJ_jVi4d.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/folder-VJ_jVi4d.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/javascript-BrEubtUq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/javascript-BrEubtUq.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/codicon.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/codicon.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/main-panel-C0suZ6dE.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/main-panel-C0suZ6dE.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-327b1cf9/DmbN.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-327b1cf9/DmbN.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/languagepacks.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/languagepacks.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/htmlMode-BuVpxTb-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/htmlMode-BuVpxTb-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/msdax-CYqgjx_P.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/msdax-CYqgjx_P.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778483707-3599a7f0-32a3-43cf-9d10-7015d337b76e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778483707-3599a7f0-32a3-43cf-9d10-7015d337b76e.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/azcli-CBeeoD2V.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/azcli-CBeeoD2V.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/markdown-9NNSJ0ww.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/markdown-9NNSJ0ww.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/c7dbbc90-c7a1-4fef-ac2b-a18eb2017393": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/c7dbbc90-c7a1-4fef-ac2b-a18eb2017393"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pascaligo-LOm9cWIk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pascaligo-LOm9cWIk.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/hash.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/hash.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/check-BSg_oTjI.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/check-BSg_oTjI.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pla-CjnFlu4u.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pla-CjnFlu4u.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778908200-296b2d8b-9d38-4a6b-97ef-940bc73c1a73.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778908200-296b2d8b-9d38-4a6b-97ef-940bc73c1a73.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/files/node/watcher/watcherMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/files/node/watcher/watcherMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-KFBOBJHC-5LIyXEHH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-KFBOBJHC-5LIyXEHH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/htmlMode-CIRhgpF_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/htmlMode-CIRhgpF_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/graphql-LQdxqEYJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/graphql-LQdxqEYJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778863044-1e71e9f8-8ac0-4c0f-b65d-c95636b81966.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778863044-1e71e9f8-8ac0-4c0f-b65d-c95636b81966.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dart-D8lhlL1r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/dart-D8lhlL1r.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/rules-BpW3ALut.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/rules-BpW3ALut.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/assignableUsers/eddie333016/BeamTechLandingPage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/assignableUsers/eddie333016/BeamTechLandingPage.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778693381-7f483feb-7d21-4489-8253-461c4f9c03c8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778693381-7f483feb-7d21-4489-8253-461c4f9c03c8.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7a75ac05/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7a75ac05/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/journeyDiagram-G5WM74LC-CP5wysXD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/journeyDiagram-G5WM74LC-CP5wysXD.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780116343-6c4fa1fa-43ed-4668-b260-10157f374750.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780116343-6c4fa1fa-43ed-4668-b260-10157f374750.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-0-305f31ed-aec6-4139-8d17-be464bef50f7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-0-305f31ed-aec6-4139-8d17-be464bef50f7.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/stateDiagram-v2-4JROLMXI-BZBSYNBo.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/stateDiagram-v2-4JROLMXI-BZBSYNBo.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chat-flags-model-bz0rqUoO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chat-flags-model-bz0rqUoO.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778619629-e461ff67-28cf-4b4c-9283-893af7ddc43c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778619629-e461ff67-28cf-4b4c-9283-893af7ddc43c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/Augment-Memories": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/Augment-Memories"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.copilot-chat/GitHub Copilot Chat.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.copilot-chat/GitHub Copilot Chat.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-0-83ef2268-fe36-4eb1-9c9b-0ed14b68949f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-0-83ef2268-fe36-4eb1-9c9b-0ed14b68949f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/machineid": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/machineid"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sb-Ddgo-Lel.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sb-Ddgo-Lel.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-0-6422f05d-6c42-41d9-a3ed-9d1232721ad1.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-0-6422f05d-6c42-41d9-a3ed-9d1232721ad1.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2752f7d5/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2752f7d5/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778202831-942a056e-c9bc-4276-a54e-f23e833f2e15.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778202831-942a056e-c9bc-4276-a54e-f23e833f2e15.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/Augment-Memories": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/Augment-Memories"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/typescript-DT13XRSV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/typescript-DT13XRSV.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_emailService.ts-1749780139082-27e1bf04-0a0b-41e9-af5a-e2fd558f3f63.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_emailService.ts-1749780139082-27e1bf04-0a0b-41e9-af5a-e2fd558f3f63.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/preference-DtXSVOsO.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/preference-DtXSVOsO.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_test-email.js-1749780492292-7490e84c-4a51-4a4b-b190-48f67763f4d3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_test-email.js-1749780492292-7490e84c-4a51-4a4b-b190-48f67763f4d3.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/erDiagram-6RL3IURR-CzfXR2wN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/erDiagram-6RL3IURR-CzfXR2wN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/folder-kNUV7Z7R.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/folder-kNUV7Z7R.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sparql-KEyrF7De.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sparql-KEyrF7De.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/.gitignore"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778908002-dcff7d13-8c63-49bb-8ea9-8b3ba36abc83.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778908002-dcff7d13-8c63-49bb-8ea9-8b3ba36abc83.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/tsMode-DM5zRHFn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/tsMode-DM5zRHFn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/go-O9LJTZXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/go-O9LJTZXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-zA0_Ky7q.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-zA0_Ky7q.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/mPS2.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/mPS2.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/log.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/log.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/objective-c-DCIC4Ga8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/objective-c-DCIC4Ga8.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-53ad80eb/CSHj.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-53ad80eb/CSHj.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780311337-cbaebc8a-3167-4b37-9794-ea21594979a3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780311337-cbaebc8a-3167-4b37-9794-ea21594979a3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/output_logging_20250613T011811/1-GitHub Copilot Log (Code References).log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/output_logging_20250613T011811/1-GitHub Copilot Log (Code References).log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/rust-DMDD0SHb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/rust-DMDD0SHb.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-T9Zo6EkU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-T9Zo6EkU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/twig-BfRIq3la.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/twig-BfRIq3la.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/LICENSE": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/LICENSE"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/design-system-init-BCZOObrS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/design-system-init-BCZOObrS.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778202831-0b0a22e3-5468-4f47-b530-9b846cc31ef8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778202831-0b0a22e3-5468-4f47-b530-9b846cc31ef8.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/xychartDiagram-6QU3TZC5-Nhrz447z.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/xychartDiagram-6QU3TZC5-Nhrz447z.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778463737-d1a73315-822a-4331-b3ac-6e9a5e73ca73.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778463737-d1a73315-822a-4331-b3ac-6e9a5e73ca73.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/server-cli.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/server-cli.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/StGF.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/StGF.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.vscode-pull-request-github/GitHub Pull Request.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.vscode-pull-request-github/GitHub Pull Request.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/kotlin-CUUhw8ZM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/kotlin-CUUhw8ZM.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778558606-7676fb09-cdf6-486e-abda-f9b7fa755da2.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778558606-7676fb09-cdf6-486e-abda-f9b7fa755da2.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/flow9-Cac8vKd7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/flow9-Cac8vKd7.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778759259-970fd507-91ca-4f6a-8c17-6ab93258d6e0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778759259-970fd507-91ca-4f6a-8c17-6ab93258d6e0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_validation.ts-1749778365514-8332b86a-6142-4ffd-b20b-b4925a754fd0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_validation.ts-1749778365514-8332b86a-6142-4ffd-b20b-b4925a754fd0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/CVCx.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/CVCx.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/augment-logo-DdgjewTP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/augment-logo-DdgjewTP.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/hcl-DxDQ3s82.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/hcl-DxDQ3s82.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778463541-88a941d6-9fde-4663-b3a4-d899fa1ce6f1.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778463541-88a941d6-9fde-4663-b3a4-d899fa1ce6f1.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lexon-Canl7DCW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lexon-Canl7DCW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pascal-DVjYFmSU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pascal-DVjYFmSU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-7U56Z5CX-rkdzNWkO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-7U56Z5CX-rkdzNWkO.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778639138-6b7bc2d9-293f-4b59-8ba6-d4ed9f6f1115.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778639138-6b7bc2d9-293f-4b59-8ba6-d4ed9f6f1115.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778665424-7de77a85-5c2c-4d03-813a-4121c44c6fdc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778665424-7de77a85-5c2c-4d03-813a-4121c44c6fdc.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/base/node/ps.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/base/node/ps.sh"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-0-66d87d6a-df0b-4533-a7d7-77abda112ef5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-0-66d87d6a-df0b-4533-a7d7-77abda112ef5.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pieDiagram-YF2LJOPJ-BYZkSE9o.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pieDiagram-YF2LJOPJ-BYZkSE9o.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sql-BdTr02Mf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sql-BdTr02Mf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sequenceDiagram-G6AWOVSC-D1yzUBJw.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sequenceDiagram-G6AWOVSC-D1yzUBJw.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7a75ac05/7ARu": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7a75ac05/7ARu"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/magnifying-glass-BKxv79dY.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/magnifying-glass-BKxv79dY.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/objective-c-BdAIHrxl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/objective-c-BdAIHrxl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pug-8ix3pnNZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pug-8ix3pnNZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.vscode-pull-request-github/GitHub Pull Request.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.vscode-pull-request-github/GitHub Pull Request.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/xml-_u1XISHN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/xml-_u1XISHN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lexon-DwtVlf1I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lexon-DwtVlf1I.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/tsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/tsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778817813-2820539d-947b-498b-8f39-91ecc703b201.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778817813-2820539d-947b-498b-8f39-91ecc703b201.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/systemverilog-DgMryOEJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/systemverilog-DgMryOEJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.copilot/GitHub Copilot.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.copilot/GitHub Copilot.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780117010-ff1ac7ae-0b11-459b-a3ab-3641fc10a710.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780117010-ff1ac7ae-0b11-459b-a3ab-3641fc10a710.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_emailService.ts-1749780139665-657bfb35-b82a-4bf6-a343-30d9b12df883.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_utils_emailService.ts-1749780139665-657bfb35-b82a-4bf6-a343-30d9b12df883.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/renameWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/renameWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/main.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/main.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/graphql-csByOneL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/graphql-csByOneL.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/requirementDiagram-MIRIMTAZ-1J1wHTOq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/requirementDiagram-MIRIMTAZ-1J1wHTOq.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778591410-28fa47eb-b0e0-45d8-b6f5-8b8362580e45.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778591410-28fa47eb-b0e0-45d8-b6f5-8b8362580e45.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_vite.ts-1749780338026-bbfee54b-0b85-4ddf-983f-ed1d73096fe0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_vite.ts-1749780338026-bbfee54b-0b85-4ddf-983f-ed1d73096fe0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-53ad80eb/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-53ad80eb/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/classDiagram-LNE6IOMH-T9Zo6EkU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/classDiagram-LNE6IOMH-T9Zo6EkU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lodash-DR_r-JLc.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lodash-DR_r-JLc.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/bootloader.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/bootloader.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.gitignore-0-ac53576c-b2d1-4f21-b0a4-cc6531eefd18.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.gitignore-0-ac53576c-b2d1-4f21-b0a4-cc6531eefd18.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_vite.ts-1749780338026-df2460c8-c369-4b21-8103-3f030e35394a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_vite.ts-1749780338026-df2460c8-c369-4b21-8103-3f030e35394a.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778679288-bb7c4876-a530-418b-b15c-c4d583f9d6f1.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778679288-bb7c4876-a530-418b-b15c-c4d583f9d6f1.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/base/node/cpuUsage.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/base/node/cpuUsage.sh"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778818375-c2a854da-c4f5-4fc6-9c45-d8f704ac3555.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778818375-c2a854da-c4f5-4fc6-9c45-d8f704ac3555.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/vb-BwAE3J76.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/vb-BwAE3J76.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/handlebars-CNQw3EXp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/handlebars-CNQw3EXp.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778711576-716bf391-1c71-4fed-900a-ba92fba6572b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778711576-716bf391-1c71-4fed-900a-ba92fba6572b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5ddef898/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5ddef898/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/yaml-CRGTkk5g.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/yaml-CRGTkk5g.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diff-utils-G5g8BFUv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diff-utils-G5g8BFUv.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.gitignore-1749780181670-b017d945-1758-443c-bfb0-d75d68aedf84.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.gitignore-1749780181670-b017d945-1758-443c-bfb0-d75d68aedf84.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/689aff0c/EPcp.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/689aff0c/EPcp.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/73012004-4dd1-4bb6-99d3-078040d77035": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/73012004-4dd1-4bb6-99d3-078040d77035"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/razor-iCuOooJL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/razor-iCuOooJL.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-603085a8-feef-4b64-ac7a-573b7b604034.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-603085a8-feef-4b64-ac7a-573b7b604034.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/bbd85d8e-3885-4eda-a472-195d68bad13b": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/bbd85d8e-3885-4eda-a472-195d68bad13b"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-45e5f64c/eXkQ.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-45e5f64c/eXkQ.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749780227664-83fc4331-2fb1-4316-bbcd-c6a01e52130a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749780227664-83fc4331-2fb1-4316-bbcd-c6a01e52130a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778908200-9346b0b2-a8f6-42a5-a986-06afa3033676.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778908200-9346b0b2-a8f6-42a5-a986-06afa3033676.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-B8aTe35M.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-B8aTe35M.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/vb-CS586MRk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/vb-CS586MRk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-McRKs1sU.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-McRKs1sU.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/output_logging_20250613T011838/1-GitHub Copilot Log (Code References).log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/output_logging_20250613T011838/1-GitHub Copilot Log (Code References).log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/autofix-DiSwwCDX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/autofix-DiSwwCDX.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/remote-agent-diff-C9S5rKqy.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/remote-agent-diff-C9S5rKqy.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/timeline-definition-U7ZMHBDA-n6Ijvk3j.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/timeline-definition-U7ZMHBDA-n6Ijvk3j.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_index.ts-1749780208066-1d90186d-023c-49b8-bcf3-4eae4311107e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_index.ts-1749780208066-1d90186d-023c-49b8-bcf3-4eae4311107e.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/gitGraph-YCYPL57B-Ny_HNwyo.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/gitGraph-YCYPL57B-Ny_HNwyo.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780117010-63b26e83-529f-4cd1-936d-1b5adfdc8b95.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_routes.ts-1749780117010-63b26e83-529f-4cd1-936d-1b5adfdc8b95.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_vite.ts-1749780337448-1bbb92be-3567-440d-b1ad-fbd6994f8b3c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_server_vite.ts-1749780337448-1bbb92be-3567-440d-b1ad-fbd6994f8b3c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/SCIF.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/SCIF.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/CachedProfilesData/__default__profile__/extensions.user.cache": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/CachedProfilesData/__default__profile__/extensions.user.cache"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/preload-helper-Dv6uf1Os.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/preload-helper-Dv6uf1Os.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/rules-DgRl5bLy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/rules-DgRl5bLy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/types-CGlLNakm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/types-CGlLNakm.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/powershell-Cz-ePiwW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/powershell-Cz-ePiwW.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778189523-9242a402-debf-4531-8708-a065afbc09e7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778189523-9242a402-debf-4531-8708-a065afbc09e7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778832664-045e63ce-2030-4188-97dd-897e1e156b1b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Testimonials.tsx-1749778832664-045e63ce-2030-4188-97dd-897e1e156b1b.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-DV5rszd2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-DV5rszd2.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mindmap-definition-GWI6TPTV-BLSJePme.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mindmap-definition-GWI6TPTV-BLSJePme.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/24165b42/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/24165b42/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/memories-A22E5Si6.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/memories-A22E5Si6.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/KT2q.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/KT2q.ts"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mcp-logo-DUT_rJ20.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mcp-logo-DUT_rJ20.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-327b1cf9/a65W.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-327b1cf9/a65W.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/remoteTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/remoteTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778650570-084ffafa-d641-4aa3-b5f5-b651fab95496.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Header.tsx-1749778650570-084ffafa-d641-4aa3-b5f5-b651fab95496.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/arrow-up-right-from-square-Df_FYENN.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/arrow-up-right-from-square-Df_FYENN.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/swift-D7IUmUK8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/swift-D7IUmUK8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scala-Bzjcj0lf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scala-Bzjcj0lf.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778136165-d6ec087b-e6f0-4f9f-a978-cfa78a40c61a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778136165-d6ec087b-e6f0-4f9f-a978-cfa78a40c61a.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/init-g68aIKmP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/init-g68aIKmP.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-B_oLNhJL.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-B_oLNhJL.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cytoscape.esm-B0yNE0-9.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/cytoscape.esm-B0yNE0-9.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778525272-d5693270-1523-4284-b866-28c41cf92c9b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778525272-d5693270-1523-4284-b866-28c41cf92c9b.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/julia-ClS8lr_N.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/julia-ClS8lr_N.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/r-DShZCeRJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/r-DShZCeRJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.copilot/GitHub Copilot.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.copilot/GitHub Copilot.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-45e5f64c/akvV.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-45e5f64c/akvV.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/preference-C_soFr3-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/preference-C_soFr3-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/shell-CsDZo4DB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/shell-CsDZo4DB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/xml-C-C41Cin.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/xml-C-C41Cin.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/CDzL.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/CDzL.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sophia-RYC1BQQz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sophia-RYC1BQQz.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/8OuW.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/8OuW.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778150538-95f4bf5a-cfe7-40d9-8898-6151e3615998.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778150538-95f4bf5a-cfe7-40d9-8898-6151e3615998.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/s0hk.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/s0hk.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/protobuf-CZXszgil.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/protobuf-CZXszgil.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/folder-opened-CX_GXeEO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/folder-opened-CX_GXeEO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sophia-dWwzI90F.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sophia-dWwzI90F.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778619397-51c3398c-bad2-4306-b1a5-bb2c92bc86ab.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778619397-51c3398c-bad2-4306-b1a5-bb2c92bc86ab.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2752f7d5/a3VM": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2752f7d5/a3VM"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-53ad80eb/boIJ.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-53ad80eb/boIJ.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/redis-DJMpkPfA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/redis-DJMpkPfA.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778863715-c05239a8-60e3-4f38-8d72-19d06fd17d21.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778863715-c05239a8-60e3-4f38-8d72-19d06fd17d21.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/428efd6e/z16j.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/428efd6e/z16j.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778120226-8128f9ae-43f0-46b9-a29b-2c668e74a9ea.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778120226-8128f9ae-43f0-46b9-a29b-2c668e74a9ea.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/m3-Bu4mmWhs.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/m3-Bu4mmWhs.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/php-120yhfDK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/php-120yhfDK.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778693967-edb7e10c-b656-463f-b280-7a81b8cc7620.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Hero.tsx-1749778693967-edb7e10c-b656-463f-b280-7a81b8cc7620.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mdx-DuAILtAS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mdx-DuAILtAS.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ruby-1H8dtvFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ruby-1H8dtvFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/restructuredtext-CghPJEOS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/restructuredtext-CghPJEOS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mdx-BaH_mmJD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/mdx-BaH_mmJD.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778558606-916e91da-bcc8-4ed8-ba1f-ed59a247dddf.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778558606-916e91da-bcc8-4ed8-ba1f-ed59a247dddf.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diff-view-Bd5SMTEy.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/diff-view-Bd5SMTEy.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/output_logging_20250613T021211/1-GitHub Copilot Log (Code References).log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/output_logging_20250613T021211/1-GitHub Copilot Log (Code References).log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-53ad80eb/fBVb.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-53ad80eb/fBVb.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/razor-BxlDHIuM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/razor-BxlDHIuM.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778119603-3e8bd18c-59a1-402f-b12d-c8a543d08081.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778119603-3e8bd18c-59a1-402f-b12d-c8a543d08081.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scheme-Dhb-2j9p.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/scheme-Dhb-2j9p.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/bootstrap-fork.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/bootstrap-fork.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/next-edit-suggestions-Bo0uqhe2.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/next-edit-suggestions-Bo0uqhe2.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.gitignore-1749780181670-9ee12012-8c3a-4e17-8aad-41c3edbac668.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.gitignore-1749780181670-9ee12012-8c3a-4e17-8aad-41c3edbac668.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749777845263-5ba11a90-5857-4ea1-986e-3f7a8582afb6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749777845263-5ba11a90-5857-4ea1-986e-3f7a8582afb6.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ini-COn9E3gi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/ini-COn9E3gi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/kanban-definition-QRCXZQQD-DuNRpSIz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/kanban-definition-QRCXZQQD-DuNRpSIz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/settings-CmzqcEdd.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/settings-CmzqcEdd.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/ZeX6.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/ZeX6.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/vscode.lock": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/vscode.lock"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sparql-CouE6pZG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sparql-CouE6pZG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-B6hSl5-9.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-B6hSl5-9.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-53ad80eb/FGeV.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-53ad80eb/FGeV.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778803721-b95bc77b-e4d4-4aab-952f-d2888eee97db.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Portfolio.tsx-1749778803721-b95bc77b-e4d4-4aab-952f-d2888eee97db.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778541597-ad3e52ad-a0fd-4adf-ae66-55feadff067d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778541597-ad3e52ad-a0fd-4adf-ae66-55feadff067d.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778427512-ee5fbe48-bfa6-4694-b291-1f99d974b70e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778427512-ee5fbe48-bfa6-4694-b291-1f99d974b70e.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/remote-agent-home-DvRbaZxy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/remote-agent-home-DvRbaZxy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/restructuredtext-CQoPj0uC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/restructuredtext-CQoPj0uC.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778463737-03bf6f86-57c1-4827-acf4-05bca6f06bb6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778463737-03bf6f86-57c1-4827-acf4-05bca6f06bb6.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778605223-13ce15c3-d369-495d-ad56-06442b0b00fd.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778605223-13ce15c3-d369-495d-ad56-06442b0b00fd.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lua-D28Ae8-K.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/lua-D28Ae8-K.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/next-edit-suggestions-Cjzqkzst.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/next-edit-suggestions-Cjzqkzst.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778498883-41bc86e4-2ebf-4837-9491-ff4564b037cc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778498883-41bc86e4-2ebf-4837-9491-ff4564b037cc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/html-Dv6uDOCE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/html-Dv6uDOCE.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5ddef898/GShD.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5ddef898/GShD.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.typescript-language-features/TypeScript.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.typescript-language-features/TypeScript.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778774684-c7faa398-d692-4049-ab8f-95a805d89766.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778774684-c7faa398-d692-4049-ab8f-95a805d89766.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/redshift-CBifECDb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/redshift-CBifECDb.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/redshift-6xAzNskS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/redshift-6xAzNskS.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778558410-963c500c-e39c-4706-8956-edcb807de07f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778558410-963c500c-e39c-4706-8956-edcb807de07f.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/types-CyEwsyyJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/types-CyEwsyyJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/less-CW-yd8b8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/less-CW-yd8b8.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/solidity-C4mwTkrB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/solidity-C4mwTkrB.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/82feb370-6d38-4b63-bdbe-1671a784a168": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/82feb370-6d38-4b63-bdbe-1671a784a168"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/urWr.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/urWr.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/wgsl-DCafy-vX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/wgsl-DCafy-vX.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778774876-06041c7e-ffeb-4032-ac30-b9b42ebb2a51.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778774876-06041c7e-ffeb-4032-ac30-b9b42ebb2a51.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778151112-4c59ded0-a17d-422f-b362-c27d590ed409.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749778151112-4c59ded0-a17d-422f-b362-c27d590ed409.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778120226-f8ae4775-1dcb-42bf-a10f-2d25e64fd746.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778120226-f8ae4775-1dcb-42bf-a10f-2d25e64fd746.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778442870-89a72f34-0504-43a4-bc85-90fa69484de7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Contact.tsx-1749778442870-89a72f34-0504-43a4-bc85-90fa69484de7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/689aff0c/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/689aff0c/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778789715-a17f42a4-01d6-44fc-aca8-9eef464b7945.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778789715-a17f42a4-01d6-44fc-aca8-9eef464b7945.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-BxQII05L.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/index-BxQII05L.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778189325-c7175197-249a-48dd-bbc4-e0fd1fec5bde.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Footer.tsx-1749778189325-c7175197-249a-48dd-bbc4-e0fd1fec5bde.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/flowDiagram-7ASYPVHJ-BJ8fVMSR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/flowDiagram-7ASYPVHJ-BJ8fVMSR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/perl-UpK8AUhB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/perl-UpK8AUhB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/history-DYX6YXEA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/history-DYX6YXEA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sb-BYAiYHFx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/sb-BYAiYHFx.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778759854-0989b30f-b9ab-4b36-94cd-c8ced92cc3ce.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_client_src_components_Services.tsx-1749778759854-0989b30f-b9ab-4b36-94cd-c8ced92cc3ce.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pgsql-cWj3SLw2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/pgsql-cWj3SLw2.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.env-1749780165624-3c306cb3-2c6d-40f5-811e-f4bd836bc254.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_.env-1749780165624-3c306cb3-2c6d-40f5-811e-f4bd836bc254.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749780227072-73ecd5bc-52fb-4d25-a2bd-c12f303ad684.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document-_home_runner_workspace_augment-guidelines.md-1749780227072-73ecd5bc-52fb-4d25-a2bd-c12f303ad684.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-TMUBEWPD-CMk4ZfiI.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/chunk-TMUBEWPD-CMk4ZfiI.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/python-CZ67Wo4I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/python-CZ67Wo4I.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/audioPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/audioPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/audioPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/audioPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/imagePreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/imagePreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/imagePreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/imagePreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/loading-hc.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/loading-hc.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/loading.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/loading.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/videoPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/videoPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/videoPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/videoPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/preview-styles/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/preview-styles/index.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/notebook-out/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/notebook-out/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/highlight.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/highlight.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/pre.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/pre.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/lib/jquery.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/lib/jquery.d.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/extension.webpack.config.cjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/extension.webpack.config.cjs"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/some-markdown.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/some-markdown.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/ignore.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/ignore.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/git-commit.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/git-commit.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/git-rebase.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/git-rebase.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/ignore.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/ignore.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/extension-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/extension-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/extension-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/extension-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-server-ready/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-server-ready/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-server-ready/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-server-ready/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/schemas/attachContainer.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/schemas/attachContainer.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/code-server": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/code-server"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli/code": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli/code"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/browser.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/browser.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/check-requirements.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/check-requirements.sh"}, "/home/<USER>/workspace/.cache/typescript/5.8/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.8/package-lock.json"}, "/home/<USER>/workspace/.cache/typescript/5.8/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.8/package.json"}, "/home/<USER>/workspace/.cache/replit/toolchain.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/toolchain.json"}, "/home/<USER>/workspace/.cache/replit/modules/replit-rtld-loader.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/replit-rtld-loader.res"}, "/home/<USER>/workspace/.cache/replit/env/latest": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest"}, "/home/<USER>/workspace/.cache/replit/env/latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest.json"}, "/home/<USER>/workspace/.cache/Microsoft/DeveloperTools/deviceid": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/Microsoft/DeveloperTools/deviceid"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/watchdog.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/watchdog.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/notebook-out/katex.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/notebook-out/katex.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/notebook-out/katex.min.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/notebook-out/katex.min.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.copilot/GitHub Copilot.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.copilot/GitHub Copilot.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/p8GM": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/p8GM"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/entries.json"}, "/home/<USER>/workspace/test-mailersend.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-mailersend.js"}, "/home/<USER>/workspace/test-sandbox.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-sandbox.js"}, "/home/<USER>/workspace/test-trial.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-trial.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/689aff0c/JorE.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/689aff0c/JorE.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/7Yeg.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/7Yeg.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/5SBr.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/5SBr.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7b682c69/FoBL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7b682c69/FoBL.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7b682c69/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7b682c69/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7b682c69/irRS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7b682c69/irRS.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7b682c69/HJfU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7b682c69/HJfU.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/Y8ip.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/Y8ip.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/Ez4j": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/Ez4j"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7b682c69/a9ry.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7b682c69/a9ry.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7b682c69/c2Z4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7b682c69/c2Z4.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/uLah": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/uLah"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2994dc39/mJaY": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2994dc39/mJaY"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2994dc39/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2994dc39/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/2jvw": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/2jvw"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/We01": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/We01"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/oII2.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/oII2.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/UAdx.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/UAdx.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/oRRn.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/oRRn.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/FNsb.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/FNsb.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/Oe1G.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/Oe1G.ts"}, "/home/<USER>/workspace/test-resend.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-resend.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-488ff9da/J1uH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-488ff9da/J1uH.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-488ff9da/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-488ff9da/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/pGbU": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/pGbU"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/akMC": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/akMC"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/6HIo.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/6HIo.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2994dc39/3pU5": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2994dc39/3pU5"}, "/home/<USER>/workspace/test-admin-email.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-admin-email.js"}}