{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}, "originalCode": "import type { Express } from \"express\";\nimport { createServer, type Server } from \"http\";\nimport { storage } from \"./storage\";\nimport { Resend } from \"resend\";\n\nexport async function registerRoutes(app: Express): Promise<Server> {\n  // Initialize Resend\n  const resend = new Resend(process.env.RESEND_API_KEY);\n\n  // Contact form submission endpoint\n  app.post(\"/api/contact\", async (req, res) => {\n    try {\n      const { name, email, company, project, budget, message } = req.body;\n\n      // Validate required fields\n      if (!name || !email || !message) {\n        return res.status(400).json({\n          success: false,\n          message: \"Name, email, and message are required fields.\"\n        });\n      }\n\n      // Email 1: Confirmation email to form submitter\n      const confirmationEmail = await resend.emails.send({\n        from: \"<EMAIL>\",\n        to: [email],\n        subject: \"Thank you for contacting Beam.tech - We'll be in touch soon\",\n        html: `\n          <!DOCTYPE html>\n          <html>\n          <head>\n            <meta charset=\"utf-8\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n            <title>Thank you for contacting Beam.tech</title>\n          </head>\n          <body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;\">\n            <div style=\"max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\">\n              <!-- Header -->\n              <div style=\"background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); padding: 40px 30px; text-align: center;\">\n                <h1 style=\"color: white; margin: 0; font-size: 28px; font-weight: bold;\">\n                  ⚡ beam.tech\n                </h1>\n                <p style=\"color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 16px;\">\n                  Web Design • Automation • AI Solutions\n                </p>\n              </div>\n\n              <!-- Content -->\n              <div style=\"padding: 40px 30px;\">\n                <h2 style=\"color: #1f2937; margin: 0 0 20px 0; font-size: 24px;\">\n                  Thank you for reaching out, ${name}!\n                </h2>\n\n                <p style=\"color: #4b5563; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;\">\n                  We've received your inquiry and are excited to learn more about your project. Our team will review your message and get back to you within 24 hours.\n                </p>\n\n                <div style=\"background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin: 20px 0;\">\n                  <h3 style=\"color: #1f2937; margin: 0 0 15px 0; font-size: 18px;\">Your Inquiry Summary:</h3>\n                  <p style=\"color: #4b5563; margin: 5px 0; font-size: 14px;\"><strong>Project Type:</strong> ${project || 'Not specified'}</p>\n                  <p style=\"color: #4b5563; margin: 5px 0; font-size: 14px;\"><strong>Budget Range:</strong> ${budget || 'Not specified'}</p>\n                  ${company ? `<p style=\"color: #4b5563; margin: 5px 0; font-size: 14px;\"><strong>Company:</strong> ${company}</p>` : ''}\n                </div>\n\n                <p style=\"color: #4b5563; line-height: 1.6; margin: 20px 0; font-size: 16px;\">\n                  In the meantime, feel free to explore our portfolio and learn more about how we've helped other businesses transform their digital presence.\n                </p>\n\n                <div style=\"text-align: center; margin: 30px 0;\">\n                  <a href=\"https://beam.tech\" style=\"display: inline-block; background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; font-size: 16px;\">\n                    Visit Our Website\n                  </a>\n                </div>\n              </div>\n\n              <!-- Footer -->\n              <div style=\"background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb;\">\n                <p style=\"color: #6b7280; margin: 0 0 10px 0; font-size: 14px;\">\n                  Made with ❤️ in Margaret River and Canberra, Australia\n                </p>\n                <p style=\"color: #9ca3af; margin: 0; font-size: 12px;\">\n                  This email was sent because you contacted us through our website. If you have any questions, reply to this email.\n                </p>\n              </div>\n            </div>\n          </body>\n          </html>\n        `)\n        .setText(`\n          Thank you for contacting Beam.tech, ${name}!\n\n          We've received your inquiry and will get back to you within 24 hours.\n\n          Your inquiry summary:\n          - Project Type: ${project || 'Not specified'}\n          - Budget Range: ${budget || 'Not specified'}\n          ${company ? `- Company: ${company}` : ''}\n\n          Best regards,\n          The Beam.tech Team\n\n          Made with ❤️ in Margaret River and Canberra, Australia\n        `);\n\n      // Email 2: Notification email to business owner\n      // Note: For trial accounts, this must be the email used to register MailerSend\n      const notificationEmailParams = new EmailParams()\n        .setFrom(new Sender(\"<EMAIL>\", \"Beam.tech Contact Form\"))\n        .setTo([new Recipient(process.env.MAILERSEND_ADMIN_EMAIL || \"<EMAIL>\", \"Eddie\")])\n        .setSubject(`New Contact Form Submission from ${name}`)\n        .setHtml(`\n          <!DOCTYPE html>\n          <html>\n          <head>\n            <meta charset=\"utf-8\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n            <title>New Contact Form Submission</title>\n          </head>\n          <body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;\">\n            <div style=\"max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\">\n              <!-- Header -->\n              <div style=\"background: linear-gradient(135deg, #dc2626 0%, #ea580c 100%); padding: 30px; text-align: center;\">\n                <h1 style=\"color: white; margin: 0; font-size: 24px; font-weight: bold;\">\n                  🚨 New Contact Form Submission\n                </h1>\n                <p style=\"color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 14px;\">\n                  beam.tech website\n                </p>\n              </div>\n\n              <!-- Content -->\n              <div style=\"padding: 30px;\">\n                <h2 style=\"color: #1f2937; margin: 0 0 20px 0; font-size: 20px;\">\n                  Contact Details\n                </h2>\n\n                <div style=\"background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin: 20px 0;\">\n                  <table style=\"width: 100%; border-collapse: collapse;\">\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151; width: 120px;\">Name:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\">${name}</td>\n                    </tr>\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151;\">Email:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\"><a href=\"mailto:${email}\" style=\"color: #2563eb; text-decoration: none;\">${email}</a></td>\n                    </tr>\n                    ${company ? `\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151;\">Company:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\">${company}</td>\n                    </tr>\n                    ` : ''}\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151;\">Project Type:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\">${project || 'Not specified'}</td>\n                    </tr>\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151;\">Budget:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\">${budget || 'Not specified'}</td>\n                    </tr>\n                  </table>\n                </div>\n\n                <h3 style=\"color: #1f2937; margin: 30px 0 15px 0; font-size: 18px;\">Message:</h3>\n                <div style=\"background-color: #f9fafb; border-left: 4px solid #2563eb; padding: 20px; margin: 0 0 20px 0;\">\n                  <p style=\"color: #374151; line-height: 1.6; margin: 0; white-space: pre-wrap;\">${message}</p>\n                </div>\n\n                <div style=\"text-align: center; margin: 30px 0;\">\n                  <a href=\"mailto:${email}?subject=Re: Your inquiry to Beam.tech\" style=\"display: inline-block; background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; font-size: 16px;\">\n                    Reply to ${name}\n                  </a>\n                </div>\n              </div>\n\n              <!-- Footer -->\n              <div style=\"background-color: #f9fafb; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;\">\n                <p style=\"color: #6b7280; margin: 0; font-size: 12px;\">\n                  Submitted on ${new Date().toLocaleString('en-AU', { timeZone: 'Australia/Perth' })} (Perth time)\n                </p>\n              </div>\n            </div>\n          </body>\n          </html>\n        `)\n        .setText(`\n          New Contact Form Submission from ${name}\n\n          Contact Details:\n          - Name: ${name}\n          - Email: ${email}\n          ${company ? `- Company: ${company}` : ''}\n          - Project Type: ${project || 'Not specified'}\n          - Budget: ${budget || 'Not specified'}\n\n          Message:\n          ${message}\n\n          Submitted on ${new Date().toLocaleString('en-AU', { timeZone: 'Australia/Perth' })} (Perth time)\n        `);\n\n      // Send both emails\n      const [confirmationResult, notificationResult] = await Promise.all([\n        mailerSend.email.send(confirmationEmailParams),\n        mailerSend.email.send(notificationEmailParams)\n      ]);\n\n      console.log('Confirmation email sent:', confirmationResult);\n      console.log('Notification email sent:', notificationResult);\n\n      res.json({\n        success: true,\n        message: \"Thank you for your message! We've sent you a confirmation email and will get back to you within 24 hours.\"\n      });\n\n    } catch (error) {\n      console.error('Email sending error:', error);\n      res.status(500).json({\n        success: false,\n        message: \"Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.\",\n        error: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : String(error)) : undefined\n      });\n    }\n  });\n\n  // use storage to perform CRUD operations on the storage interface\n  // e.g. storage.insertUser(user) or storage.getUserByUsername(username)\n\n  const httpServer = createServer(app);\n\n  return httpServer;\n}\n", "modifiedCode": "import type { Express } from \"express\";\nimport { createServer, type Server } from \"http\";\nimport { storage } from \"./storage\";\nimport { Resend } from \"resend\";\n\nexport async function registerRoutes(app: Express): Promise<Server> {\n  // Initialize Resend\n  const resend = new Resend(process.env.RESEND_API_KEY);\n\n  // Contact form submission endpoint\n  app.post(\"/api/contact\", async (req, res) => {\n    try {\n      const { name, email, company, project, budget, message } = req.body;\n\n      // Validate required fields\n      if (!name || !email || !message) {\n        return res.status(400).json({\n          success: false,\n          message: \"Name, email, and message are required fields.\"\n        });\n      }\n\n      // Email 1: Confirmation email to form submitter\n      const confirmationEmail = await resend.emails.send({\n        from: \"<EMAIL>\",\n        to: [email],\n        subject: \"Thank you for contacting Beam.tech - We'll be in touch soon\",\n        html: `\n          <!DOCTYPE html>\n          <html>\n          <head>\n            <meta charset=\"utf-8\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n            <title>Thank you for contacting Beam.tech</title>\n          </head>\n          <body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;\">\n            <div style=\"max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\">\n              <!-- Header -->\n              <div style=\"background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); padding: 40px 30px; text-align: center;\">\n                <h1 style=\"color: white; margin: 0; font-size: 28px; font-weight: bold;\">\n                  ⚡ beam.tech\n                </h1>\n                <p style=\"color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 16px;\">\n                  Web Design • Automation • AI Solutions\n                </p>\n              </div>\n\n              <!-- Content -->\n              <div style=\"padding: 40px 30px;\">\n                <h2 style=\"color: #1f2937; margin: 0 0 20px 0; font-size: 24px;\">\n                  Thank you for reaching out, ${name}!\n                </h2>\n\n                <p style=\"color: #4b5563; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;\">\n                  We've received your inquiry and are excited to learn more about your project. Our team will review your message and get back to you within 24 hours.\n                </p>\n\n                <div style=\"background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin: 20px 0;\">\n                  <h3 style=\"color: #1f2937; margin: 0 0 15px 0; font-size: 18px;\">Your Inquiry Summary:</h3>\n                  <p style=\"color: #4b5563; margin: 5px 0; font-size: 14px;\"><strong>Project Type:</strong> ${project || 'Not specified'}</p>\n                  <p style=\"color: #4b5563; margin: 5px 0; font-size: 14px;\"><strong>Budget Range:</strong> ${budget || 'Not specified'}</p>\n                  ${company ? `<p style=\"color: #4b5563; margin: 5px 0; font-size: 14px;\"><strong>Company:</strong> ${company}</p>` : ''}\n                </div>\n\n                <p style=\"color: #4b5563; line-height: 1.6; margin: 20px 0; font-size: 16px;\">\n                  In the meantime, feel free to explore our portfolio and learn more about how we've helped other businesses transform their digital presence.\n                </p>\n\n                <div style=\"text-align: center; margin: 30px 0;\">\n                  <a href=\"https://beam.tech\" style=\"display: inline-block; background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; font-size: 16px;\">\n                    Visit Our Website\n                  </a>\n                </div>\n              </div>\n\n              <!-- Footer -->\n              <div style=\"background-color: #f9fafb; padding: 30px; text-align: center; border-top: 1px solid #e5e7eb;\">\n                <p style=\"color: #6b7280; margin: 0 0 10px 0; font-size: 14px;\">\n                  Made with ❤️ in Margaret River and Canberra, Australia\n                </p>\n                <p style=\"color: #9ca3af; margin: 0; font-size: 12px;\">\n                  This email was sent because you contacted us through our website. If you have any questions, reply to this email.\n                </p>\n              </div>\n            </div>\n          </body>\n          </html>\n        `)\n        .setText(`\n          Thank you for contacting Beam.tech, ${name}!\n\n          We've received your inquiry and will get back to you within 24 hours.\n\n          Your inquiry summary:\n          - Project Type: ${project || 'Not specified'}\n          - Budget Range: ${budget || 'Not specified'}\n          ${company ? `- Company: ${company}` : ''}\n\n          Best regards,\n          The Beam.tech Team\n\n          Made with ❤️ in Margaret River and Canberra, Australia\n        `);\n\n      // Email 2: Notification email to business owner\n      // Note: For trial accounts, this must be the email used to register MailerSend\n      const notificationEmailParams = new EmailParams()\n        .setFrom(new Sender(\"<EMAIL>\", \"Beam.tech Contact Form\"))\n        .setTo([new Recipient(process.env.MAILERSEND_ADMIN_EMAIL || \"<EMAIL>\", \"Eddie\")])\n        .setSubject(`New Contact Form Submission from ${name}`)\n        .setHtml(`\n          <!DOCTYPE html>\n          <html>\n          <head>\n            <meta charset=\"utf-8\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n            <title>New Contact Form Submission</title>\n          </head>\n          <body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8fafc;\">\n            <div style=\"max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\">\n              <!-- Header -->\n              <div style=\"background: linear-gradient(135deg, #dc2626 0%, #ea580c 100%); padding: 30px; text-align: center;\">\n                <h1 style=\"color: white; margin: 0; font-size: 24px; font-weight: bold;\">\n                  🚨 New Contact Form Submission\n                </h1>\n                <p style=\"color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 14px;\">\n                  beam.tech website\n                </p>\n              </div>\n\n              <!-- Content -->\n              <div style=\"padding: 30px;\">\n                <h2 style=\"color: #1f2937; margin: 0 0 20px 0; font-size: 20px;\">\n                  Contact Details\n                </h2>\n\n                <div style=\"background-color: #f3f4f6; border-radius: 8px; padding: 20px; margin: 20px 0;\">\n                  <table style=\"width: 100%; border-collapse: collapse;\">\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151; width: 120px;\">Name:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\">${name}</td>\n                    </tr>\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151;\">Email:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\"><a href=\"mailto:${email}\" style=\"color: #2563eb; text-decoration: none;\">${email}</a></td>\n                    </tr>\n                    ${company ? `\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151;\">Company:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\">${company}</td>\n                    </tr>\n                    ` : ''}\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151;\">Project Type:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\">${project || 'Not specified'}</td>\n                    </tr>\n                    <tr>\n                      <td style=\"padding: 8px 0; font-weight: bold; color: #374151;\">Budget:</td>\n                      <td style=\"padding: 8px 0; color: #1f2937;\">${budget || 'Not specified'}</td>\n                    </tr>\n                  </table>\n                </div>\n\n                <h3 style=\"color: #1f2937; margin: 30px 0 15px 0; font-size: 18px;\">Message:</h3>\n                <div style=\"background-color: #f9fafb; border-left: 4px solid #2563eb; padding: 20px; margin: 0 0 20px 0;\">\n                  <p style=\"color: #374151; line-height: 1.6; margin: 0; white-space: pre-wrap;\">${message}</p>\n                </div>\n\n                <div style=\"text-align: center; margin: 30px 0;\">\n                  <a href=\"mailto:${email}?subject=Re: Your inquiry to Beam.tech\" style=\"display: inline-block; background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%); color: white; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; font-size: 16px;\">\n                    Reply to ${name}\n                  </a>\n                </div>\n              </div>\n\n              <!-- Footer -->\n              <div style=\"background-color: #f9fafb; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;\">\n                <p style=\"color: #6b7280; margin: 0; font-size: 12px;\">\n                  Submitted on ${new Date().toLocaleString('en-AU', { timeZone: 'Australia/Perth' })} (Perth time)\n                </p>\n              </div>\n            </div>\n          </body>\n          </html>\n        `)\n        .setText(`\n          New Contact Form Submission from ${name}\n\n          Contact Details:\n          - Name: ${name}\n          - Email: ${email}\n          ${company ? `- Company: ${company}` : ''}\n          - Project Type: ${project || 'Not specified'}\n          - Budget: ${budget || 'Not specified'}\n\n          Message:\n          ${message}\n\n          Submitted on ${new Date().toLocaleString('en-AU', { timeZone: 'Australia/Perth' })} (Perth time)\n        `);\n\n      // Send both emails\n      const [confirmationResult, notificationResult] = await Promise.all([\n        mailerSend.email.send(confirmationEmailParams),\n        mailerSend.email.send(notificationEmailParams)\n      ]);\n\n      console.log('Confirmation email sent:', confirmationResult);\n      console.log('Notification email sent:', notificationResult);\n\n      res.json({\n        success: true,\n        message: \"Thank you for your message! We've sent you a confirmation email and will get back to you within 24 hours.\"\n      });\n\n    } catch (error) {\n      console.error('Email sending error:', error);\n      res.status(500).json({\n        success: false,\n        message: \"Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.\",\n        error: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : String(error)) : undefined\n      });\n    }\n  });\n\n  // use storage to perform CRUD operations on the storage interface\n  // e.g. storage.insertUser(user) or storage.getUserByUsername(username)\n\n  const httpServer = createServer(app);\n\n  return httpServer;\n}\n"}