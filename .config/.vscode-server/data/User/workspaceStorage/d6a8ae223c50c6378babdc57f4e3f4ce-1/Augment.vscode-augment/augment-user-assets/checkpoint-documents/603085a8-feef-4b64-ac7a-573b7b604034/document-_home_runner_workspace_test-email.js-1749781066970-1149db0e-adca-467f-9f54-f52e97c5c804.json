{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-email.js"}, "originalCode": "// Test script to verify MailerSend integration\nimport { <PERSON><PERSON><PERSON><PERSON>, Email<PERSON><PERSON><PERSON>, Sender, Recipient } from \"mailersend\";\nimport dotenv from \"dotenv\";\n\ndotenv.config();\n\nconst mailerSend = new MailerSend({\n  apiKey: process.env.MAILERSEND_API_TOKEN || \"mlsn.2d97cb9a76439b49d03b4ecd2239f3ad6874b7758fb206916c49df25c35de2e2\",\n});\n\nasync function testEmail() {\n  try {\n    console.log(\"Testing MailerSend integration...\");\n    \n    const emailParams = new EmailParams()\n      .setFrom(new Sender(\"<EMAIL>\", \"Beam.tech Test\"))\n      .setTo([new Recipient(\"<EMAIL>\", \"Eddie\")])\n      .setSubject(\"Test Email from Beam.tech Contact Form\")\n      .setHtml(`\n        <h1>Test Email</h1>\n        <p>This is a test email to verify MailerSend integration is working.</p>\n        <p>If you receive this, the email service is configured correctly!</p>\n      `)\n      .setText(\"Test email - MailerSend integration is working!\");\n\n    const result = await mailerSend.email.send(emailParams);\n    console.log(\"Email sent successfully:\", result);\n    \n  } catch (error) {\n    console.error(\"Email sending failed:\", error);\n  }\n}\n\ntestEmail();\n"}