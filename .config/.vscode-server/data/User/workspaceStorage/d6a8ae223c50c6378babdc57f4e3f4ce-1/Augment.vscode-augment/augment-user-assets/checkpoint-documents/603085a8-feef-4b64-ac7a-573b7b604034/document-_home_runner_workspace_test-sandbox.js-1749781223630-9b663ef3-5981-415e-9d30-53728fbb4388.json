{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-sandbox.js"}, "modifiedCode": "// Test script using MailerSend sandbox mode (works without domain verification)\nimport { Mailer<PERSON><PERSON>, <PERSON>ailParams, Sender, Recipient } from \"mailersend\";\nimport dotenv from \"dotenv\";\n\ndotenv.config();\n\nconst mailerSend = new MailerSend({\n  apiKey: process.env.MAILERSEND_API_TOKEN || \"mlsn.2d97cb9a76439b49d03b4ecd2239f3ad6874b7758fb206916c49df25c35de2e2\",\n});\n\nasync function testSandboxEmail() {\n  try {\n    console.log(\"🧪 Testing MailerSend in SANDBOX mode...\");\n    console.log(\"API Token:\", process.env.MAILERSEND_API_TOKEN ? \"✅ Found\" : \"❌ Missing\");\n    \n    // Use sandbox mode - emails won't actually be delivered but API will respond successfully\n    const emailParams = new EmailParams()\n      .setFrom(new Sender(\"<EMAIL>\", \"Beam.tech Test\"))\n      .setTo([new Recipient(\"<EMAIL>\", \"Test User\")]) // Use example.com for testing\n      .setSubject(\"Test Email from Beam.tech Contact Form (Sandbox)\")\n      .setHtml(`\n        <h1>🎉 Sandbox Test Email</h1>\n        <p>This is a sandbox test email to verify MailerSend integration.</p>\n        <p><strong>Note:</strong> This email won't actually be delivered (sandbox mode)</p>\n        <p><strong>Time:</strong> ${new Date().toISOString()}</p>\n      `)\n      .setText(\"Sandbox test email - MailerSend integration test. Sent at: \" + new Date().toISOString());\n\n    console.log(\"📧 Attempting to send sandbox email...\");\n    const result = await mailerSend.email.send(emailParams);\n    console.log(\"✅ Sandbox email sent successfully!\");\n    console.log(\"Response:\", result);\n    console.log(\"\\n📝 Note: This was a sandbox test. No actual email was delivered.\");\n    \n  } catch (error) {\n    console.error(\"❌ Sandbox email failed:\");\n    console.error(\"Status Code:\", error.statusCode);\n    console.error(\"Error Message:\", error.body?.message);\n    console.log(\"Full error details:\", error);\n  }\n}\n\ntestSandboxEmail();\n"}