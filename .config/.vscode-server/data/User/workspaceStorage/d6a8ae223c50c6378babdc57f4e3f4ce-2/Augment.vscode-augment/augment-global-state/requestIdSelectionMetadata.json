[["514f8583-7f9a-4de4-b81a-2a8faee56bdd", {"value": {"selectedCode": "", "prefix": "# Resend API Configuration\n", "suffix": "RESEND_API_KEY=re_GnJ6mX7b_NaZKsS4UXw4YPNi8RhaGSLDL\n# Notification email address\nNOTIFICATION_EMAIL=<EMAIL>\n\n# Database Configuration\nDATABASE_URL=your_database_url_here\n\n# Environment\nNODE_ENV=development\n", "path": ".env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["1ea41255-d001-49b7-b331-a058d4df22b4", {"value": {"selectedCode": "", "prefix": "# Resend API Configuration\n", "suffix": "RESEND_API_KEY=re_GnJ6mX7b_NaZKsS4UXw4YPNi8RhaGSLDL\n# Notification email address\nNOTIFICATION_EMAIL=<EMAIL>\n\n# Database Configuration\nDATABASE_URL=your_database_url_here\n\n# Environment\nNODE_ENV=development\n", "path": ".env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}]]