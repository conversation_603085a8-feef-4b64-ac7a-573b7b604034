{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/emailService.ts"}, "originalCode": "// Email service utilities for form submissions and newsletter\n\nexport interface EmailServiceResponse {\n  success: boolean;\n  message: string;\n  error?: string;\n}\n\nexport interface ContactFormSubmission {\n  name: string;\n  email: string;\n  company: string;\n  project: string;\n  budget: string;\n  message: string;\n}\n\nexport interface NewsletterSubmission {\n  email: string;\n}\n\n// For production, you would replace this with actual email service integration\n// Options include: EmailJS, Formspree, Netlify Forms, or your own backend API\n\nexport const submitContactForm = async (data: ContactFormSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Send contact form data to our backend API\n    const response = await fetch('/api/contact', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      // If MailerSend domain verification fails, provide helpful error message\n      if (result.message?.includes('verified domains')) {\n        throw new Error('Email service setup required. Please contact us <NAME_EMAIL> while we complete our email configuration.');\n      }\n      throw new Error(result.message || 'Failed to send message');\n    }\n\n    return {\n      success: true,\n      message: result.message || 'Thank you for your message! We\\'ll get back to you within 24 hours.'\n    };\n  } catch (error) {\n    console.error('Contact form submission error:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\nexport const subscribeToNewsletter = async (data: NewsletterSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    // For demo purposes, we'll simulate success\n    // In production, replace this with actual newsletter service integration\n    \n    // Example Mailchimp integration:\n    // const response = await fetch('/api/newsletter/subscribe', {\n    //   method: 'POST',\n    //   headers: {\n    //     'Content-Type': 'application/json',\n    //   },\n    //   body: JSON.stringify({ email: data.email }),\n    // });\n\n    // Example ConvertKit integration:\n    // const response = await fetch('https://api.convertkit.com/v3/forms/YOUR_FORM_ID/subscribe', {\n    //   method: 'POST',\n    //   headers: {\n    //     'Content-Type': 'application/json',\n    //   },\n    //   body: JSON.stringify({\n    //     api_key: 'YOUR_API_KEY',\n    //     email: data.email\n    //   }),\n    // });\n\n    console.log('Newsletter subscription:', data);\n\n    // Simulate occasional failures for testing\n    if (Math.random() < 0.05) {\n      throw new Error('Subscription service temporarily unavailable');\n    }\n\n    return {\n      success: true,\n      message: 'Successfully subscribed! Check your email for confirmation.'\n    };\n  } catch (error) {\n    console.error('Newsletter subscription error:', error);\n    return {\n      success: false,\n      message: 'Sorry, there was an error with your subscription. Please try again.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n", "modifiedCode": "// Email service utilities for form submissions and newsletter\n\nexport interface EmailServiceResponse {\n  success: boolean;\n  message: string;\n  error?: string;\n}\n\nexport interface ContactFormSubmission {\n  name: string;\n  email: string;\n  company: string;\n  project: string;\n  budget: string;\n  message: string;\n}\n\nexport interface NewsletterSubmission {\n  email: string;\n}\n\n// For production, you would replace this with actual email service integration\n// Options include: EmailJS, Formspree, Netlify Forms, or your own backend API\n\nexport const submitContactForm = async (data: ContactFormSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Send contact form data to our backend API\n    const response = await fetch('/api/contact', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      // If MailerSend domain verification fails, provide helpful error message\n      if (result.message?.includes('verified domains')) {\n        throw new Error('Email service setup required. Please contact us <NAME_EMAIL> while we complete our email configuration.');\n      }\n      throw new Error(result.message || 'Failed to send message');\n    }\n\n    return {\n      success: true,\n      message: result.message || 'Thank you for your message! We\\'ll get back to you within 24 hours.'\n    };\n  } catch (error) {\n    console.error('Contact form submission error:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\nexport const subscribeToNewsletter = async (data: NewsletterSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    // For demo purposes, we'll simulate success\n    // In production, replace this with actual newsletter service integration\n    \n    // Example Mailchimp integration:\n    // const response = await fetch('/api/newsletter/subscribe', {\n    //   method: 'POST',\n    //   headers: {\n    //     'Content-Type': 'application/json',\n    //   },\n    //   body: JSON.stringify({ email: data.email }),\n    // });\n\n    // Example ConvertKit integration:\n    // const response = await fetch('https://api.convertkit.com/v3/forms/YOUR_FORM_ID/subscribe', {\n    //   method: 'POST',\n    //   headers: {\n    //     'Content-Type': 'application/json',\n    //   },\n    //   body: JSON.stringify({\n    //     api_key: 'YOUR_API_KEY',\n    //     email: data.email\n    //   }),\n    // });\n\n    console.log('Newsletter subscription:', data);\n\n    // Simulate occasional failures for testing\n    if (Math.random() < 0.05) {\n      throw new Error('Subscription service temporarily unavailable');\n    }\n\n    return {\n      success: true,\n      message: 'Successfully subscribed! Check your email for confirmation.'\n    };\n  } catch (error) {\n    console.error('Newsletter subscription error:', error);\n    return {\n      success: false,\n      message: 'Sorry, there was an error with your subscription. Please try again.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n"}