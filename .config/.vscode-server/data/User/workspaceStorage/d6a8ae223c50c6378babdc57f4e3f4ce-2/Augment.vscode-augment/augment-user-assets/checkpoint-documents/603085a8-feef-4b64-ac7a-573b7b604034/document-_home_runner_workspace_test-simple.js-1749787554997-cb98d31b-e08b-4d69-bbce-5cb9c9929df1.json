{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-simple.js"}, "originalCode": "// Simple test for <NAME_EMAIL>\nimport { Resend } from \"resend\";\nimport dotenv from \"dotenv\";\n\ndotenv.config();\n\nasync function testSimple() {\n  console.log(\"🧪 Testing <NAME_EMAIL>...\");\n  \n  const resend = new Resend(process.env.RESEND_API_KEY);\n  \n  try {\n    const result = await resend.emails.send({\n      from: \"<EMAIL>\",\n      to: [\"<EMAIL>\"],\n      subject: \"Test Email - Beam.tech Contact Form\",\n      html: \"<h1>✅ Success!</h1><p>Your email integration is <NAME_EMAIL></p>\",\n      text: \"Success! Your email integration is <NAME_EMAIL>\"\n    });\n\n    console.log(\"✅ SUCCESS!\");\n    console.log(\"Message ID:\", result.data?.id);\n    console.log(\"Check <EMAIL> for the email!\");\n    \n  } catch (error) {\n    console.log(\"❌ Error:\", error.message);\n  }\n}\n\ntestSimple();\n"}