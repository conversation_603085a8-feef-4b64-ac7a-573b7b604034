{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "vite.config.ts"}, "originalCode": "import { defineConfig } from \"vite\";\nimport react from \"@vitejs/plugin-react\";\nimport path from \"path\";\nimport runtimeErrorOverlay from \"@replit/vite-plugin-runtime-error-modal\";\n\nexport default defineConfig({\n  plugins: [\n    react(),\n    runtimeErrorOverlay(),\n    ...(process.env.NODE_ENV !== \"production\" &&\n    process.env.REPL_ID !== undefined\n      ? [\n          await import(\"@replit/vite-plugin-cartographer\").then((m) =>\n            m.cartographer(),\n          ),\n        ]\n      : []),\n  ],\n  resolve: {\n    alias: {\n      \"@\": path.resolve(import.meta.dirname, \"client\", \"src\"),\n      \"@shared\": path.resolve(import.meta.dirname, \"shared\"),\n      \"@assets\": path.resolve(import.meta.dirname, \"attached_assets\"),\n    },\n  },\n  root: path.resolve(import.meta.dirname, \"client\"),\n  build: {\n    outDir: path.resolve(import.meta.dirname, \"dist/public\"),\n    emptyOutDir: true,\n  },\n  server: {\n    host: \"0.0.0.0\",\n    port: 5173,\n    allowedHosts: [\n      \"localhost\",\n      \".replit.dev\",\n      \".repl.co\",\n      /^.*\\.replit\\.dev$/,\n      /^.*\\.repl\\.co$/,\n      // Add the specific Replit host from your error\n      \"b2161dd0-34bc-4a24-b133-e6c5b2a17565-00-3c4947wixk6vb.riker.replit.dev\"\n    ],\n  },\n});\n", "modifiedCode": "import { defineConfig } from \"vite\";\nimport react from \"@vitejs/plugin-react\";\nimport path from \"path\";\nimport runtimeErrorOverlay from \"@replit/vite-plugin-runtime-error-modal\";\n\nexport default defineConfig({\n  plugins: [\n    react(),\n    runtimeErrorOverlay(),\n    ...(process.env.NODE_ENV !== \"production\" &&\n    process.env.REPL_ID !== undefined\n      ? [\n          await import(\"@replit/vite-plugin-cartographer\").then((m) =>\n            m.cartographer(),\n          ),\n        ]\n      : []),\n  ],\n  resolve: {\n    alias: {\n      \"@\": path.resolve(import.meta.dirname, \"client\", \"src\"),\n      \"@shared\": path.resolve(import.meta.dirname, \"shared\"),\n      \"@assets\": path.resolve(import.meta.dirname, \"attached_assets\"),\n    },\n  },\n  root: path.resolve(import.meta.dirname, \"client\"),\n  build: {\n    outDir: path.resolve(import.meta.dirname, \"dist/public\"),\n    emptyOutDir: true,\n  },\n  server: {\n    host: \"0.0.0.0\",\n    port: 5173,\n    allowedHosts: [\n      \"localhost\",\n      \".replit.dev\",\n      \".repl.co\",\n      /^.*\\.replit\\.dev$/,\n      /^.*\\.repl\\.co$/,\n      // Add the specific Replit host from your error\n      \"b2161dd0-34bc-4a24-b133-e6c5b2a17565-00-3c4947wixk6vb.riker.replit.dev\"\n    ],\n  },\n});\n"}