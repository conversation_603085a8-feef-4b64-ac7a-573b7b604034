{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Footer.tsx"}, "originalCode": "import React from 'react';\nimport { Zap, Mail, Phone, MapPin, ArrowUp } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const quickLinks = [\n    { name: 'About', href: '#about' },\n    { name: 'Services', href: '#services' },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Technology', href: '#tech' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const services = [\n    { name: 'Web Design', href: '#services' },\n    { name: 'Web Development', href: '#services' },\n    { name: 'Business Automation', href: '#services' },\n    { name: 'AI Solutions', href: '#services' },\n    { name: 'Consulting', href: '#contact' }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16 grid lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"relative\">\n                <Zap className=\"h-8 w-8 text-purple-400\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                beam.tech\n              </span>\n            </div>\n            <p className=\"text-gray-300 leading-relaxed mb-6\">\n              Transforming businesses through cutting-edge web design, automation, and AI solutions. \n              We beam your business to the future.\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Mail className=\"h-5 w-5 text-purple-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Phone className=\"h-5 w-5 text-purple-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <MapPin className=\"h-5 w-5 text-purple-400\" />\n                <span>Margaret River and Canberra, Australia</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Quick Links</h3>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Services</h3>\n            <ul className=\"space-y-3\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <a\n                    href={service.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {service.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Newsletter */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Stay Updated</h3>\n            <p className=\"text-gray-300 mb-4\">\n              Get the latest insights on web design, automation, and AI trends.\n            </p>\n            <div className=\"space-y-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 rounded-lg hover:shadow-lg transition-all duration-200\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2024 Beam.tech. All rights reserved. Made in Margaret River and Canberra, Australia.\n            </div>\n            \n            <div className=\"flex items-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Privacy Policy\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Terms of Service\n              </a>\n              <button\n                onClick={scrollToTop}\n                className=\"p-2 bg-gray-800 hover:bg-purple-600 rounded-lg text-gray-400 hover:text-white transition-all duration-200\"\n                aria-label=\"Scroll to top\"\n              >\n                <ArrowUp className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;", "modifiedCode": "import React from 'react';\nimport { Zap, Mail, Phone, MapPin, ArrowUp } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const quickLinks = [\n    { name: 'About', href: '#about' },\n    { name: 'Services', href: '#services' },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Technology', href: '#tech' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const services = [\n    { name: 'Web Design', href: '#services' },\n    { name: 'Web Development', href: '#services' },\n    { name: 'Business Automation', href: '#services' },\n    { name: 'AI Solutions', href: '#services' },\n    { name: 'Consulting', href: '#contact' }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16 grid lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"relative\">\n                <Zap className=\"h-8 w-8 text-purple-400\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                beam.tech\n              </span>\n            </div>\n            <p className=\"text-gray-300 leading-relaxed mb-6\">\n              Transforming businesses through cutting-edge web design, automation, and AI solutions. \n              We beam your business to the future.\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Mail className=\"h-5 w-5 text-purple-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Phone className=\"h-5 w-5 text-purple-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <MapPin className=\"h-5 w-5 text-purple-400\" />\n                <span>Margaret River and Canberra, Australia</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Quick Links</h3>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Services</h3>\n            <ul className=\"space-y-3\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <a\n                    href={service.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {service.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Newsletter */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Stay Updated</h3>\n            <p className=\"text-gray-300 mb-4\">\n              Get the latest insights on web design, automation, and AI trends.\n            </p>\n            <div className=\"space-y-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 rounded-lg hover:shadow-lg transition-all duration-200\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2024 Beam.tech. All rights reserved. Made in Margaret River and Canberra, Australia.\n            </div>\n            \n            <div className=\"flex items-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Privacy Policy\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Terms of Service\n              </a>\n              <button\n                onClick={scrollToTop}\n                className=\"p-2 bg-gray-800 hover:bg-purple-600 rounded-lg text-gray-400 hover:text-white transition-all duration-200\"\n                aria-label=\"Scroll to top\"\n              >\n                <ArrowUp className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;"}