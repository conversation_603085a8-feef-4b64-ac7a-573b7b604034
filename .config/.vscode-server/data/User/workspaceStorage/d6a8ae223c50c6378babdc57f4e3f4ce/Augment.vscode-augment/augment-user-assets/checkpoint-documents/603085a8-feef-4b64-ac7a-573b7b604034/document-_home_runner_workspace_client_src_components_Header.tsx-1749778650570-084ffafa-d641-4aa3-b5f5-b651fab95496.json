{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Header.tsx"}, "originalCode": "import React, { useState, useEffect } from 'react';\nimport { Menu, X, Sun, Moon, Zap } from 'lucide-react';\nimport { useTheme } from '../hooks/useTheme';\n\nconst Header: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const { isDark, toggleTheme } = useTheme();\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Home', href: '#hero' },\n    { name: 'Services', href: '#services' },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Technology', href: '#tech' },\n    { name: 'Testimonials', href: '#testimonials' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  return (\n    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      isScrolled \n        ? 'bg-white/90 dark:bg-gray-900/90 backdrop-blur-md shadow-lg' \n        : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"relative\">\n              <Zap className=\"h-8 w-8 text-purple-600 dark:text-purple-400\" />\n              <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n            </div>\n            <span className=\"text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">\n              beam.tech\n            </span>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200 font-medium\"\n              >\n                {item.name}\n              </a>\n            ))}\n          </nav>\n\n          {/* Theme Toggle & Mobile Menu */}\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={toggleTheme}\n              className=\"p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200\"\n            >\n              {isDark ? <Sun className=\"h-5 w-5\" /> : <Moon className=\"h-5 w-5\" />}\n            </button>\n            \n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400\"\n            >\n              {isMenuOpen ? <X className=\"h-5 w-5\" /> : <Menu className=\"h-5 w-5\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden absolute top-full left-0 right-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg\">\n            <nav className=\"px-4 py-4 space-y-3\">\n              {navItems.map((item) => (\n                <a\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block py-2 text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200 font-medium\"\n                >\n                  {item.name}\n                </a>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;", "modifiedCode": "import React, { useState, useEffect } from 'react';\nimport { Menu, X, Sun, Moon, Zap } from 'lucide-react';\nimport { useTheme } from '../hooks/useTheme';\nimport { handleNavClick } from '../utils/navigation';\n\nconst Header: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const { isDark, toggleTheme } = useTheme();\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Home', href: '#hero' },\n    { name: 'Services', href: '#services' },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Technology', href: '#tech' },\n    { name: 'Testimonials', href: '#testimonials' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  return (\n    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      isScrolled \n        ? 'bg-white/90 dark:bg-gray-900/90 backdrop-blur-md shadow-lg' \n        : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"relative\">\n              <Zap className=\"h-8 w-8 text-purple-600 dark:text-purple-400\" />\n              <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n            </div>\n            <span className=\"text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">\n              beam.tech\n            </span>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200 font-medium\"\n              >\n                {item.name}\n              </a>\n            ))}\n          </nav>\n\n          {/* Theme Toggle & Mobile Menu */}\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={toggleTheme}\n              className=\"p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200\"\n            >\n              {isDark ? <Sun className=\"h-5 w-5\" /> : <Moon className=\"h-5 w-5\" />}\n            </button>\n            \n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400\"\n            >\n              {isMenuOpen ? <X className=\"h-5 w-5\" /> : <Menu className=\"h-5 w-5\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden absolute top-full left-0 right-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg\">\n            <nav className=\"px-4 py-4 space-y-3\">\n              {navItems.map((item) => (\n                <a\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block py-2 text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200 font-medium\"\n                >\n                  {item.name}\n                </a>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;"}