{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Hero.tsx"}, "originalCode": "import React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Bot, Sparkles } from 'lucide-react';\nimport { scrollToContact, scrollToPortfolio } from '../utils/navigation';\n\nconst Hero: React.FC = () => {\n  return (\n    <section id=\"hero\" className=\"min-h-screen relative overflow-hidden bg-gradient-to-br from-purple-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-purple-300 dark:bg-purple-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute top-40 right-10 w-72 h-72 bg-blue-300 dark:bg-blue-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute bottom-20 left-20 w-72 h-72 bg-pink-300 dark:bg-pink-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center min-h-screen\">\n          {/* Left Content */}\n          <div className=\"space-y-8\">\n            <div className=\"space-y-4\">\n              <div className=\"inline-flex items-center space-x-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full px-4 py-2 text-sm font-medium text-purple-600 dark:text-purple-400 border border-purple-200 dark:border-purple-800\">\n                <Sparkles className=\"h-4 w-4\" />\n                <span>Cutting-edge Technology Solutions</span>\n              </div>\n              \n              <h1 className=\"text-5xl lg:text-7xl font-bold leading-tight\">\n                <span className=\"bg-gradient-to-r from-purple-600 via-blue-600 to-purple-600 bg-clip-text text-transparent animate-gradient-x\">\n                  Beam Your\n                </span>\n                <br />\n                <span className=\"text-gray-900 dark:text-white\">\n                  Business to the\n                </span>\n                <br />\n                <span className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent animate-gradient-x\">\n                  Future\n                </span>\n              </h1>\n              \n              <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-2xl leading-relaxed\">\n                We craft exceptional web experiences, automate complex workflows, and harness AI to transform your business. Join the revolution of intelligent digital solutions.\n              </p>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <button className=\"group relative inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\">\n                <span>Start Your Project</span>\n                <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200\" />\n              </button>\n              \n              <button className=\"inline-flex items-center justify-center px-8 py-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-900 dark:text-white font-semibold rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-white dark:hover:bg-gray-800 transform hover:-translate-y-1 transition-all duration-300\">\n                View Our Work\n              </button>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 pt-8\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600 dark:text-purple-400\">500+</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Projects Delivered</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600 dark:text-blue-400\">98%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Client Satisfaction</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600 dark:text-purple-400\">24/7</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Support Available</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content - Floating Cards */}\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 rounded-3xl transform rotate-6 opacity-20\"></div>\n            \n            <div className=\"relative grid grid-cols-1 gap-6\">\n              {/* Web Design Card */}\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl transform hover:-translate-y-2 transition-all duration-300 border border-white/20 dark:border-gray-700/20\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg\">\n                    <Code className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 dark:text-white\">Web Design</h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">Modern, responsive interfaces</p>\n                  </div>\n                </div>\n              </div>\n\n              {/* AI Solutions Card */}\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl transform hover:-translate-y-2 transition-all duration-300 border border-white/20 dark:border-gray-700/20 ml-8\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg\">\n                    <Bot className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 dark:text-white\">AI Solutions</h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">Intelligent automation tools</p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Automation Card */}\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl transform hover:-translate-y-2 transition-all duration-300 border border-white/20 dark:border-gray-700/20\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg\">\n                    <Sparkles className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 dark:text-white\">Automation</h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">Streamlined workflows</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;", "modifiedCode": "import React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Bot, Sparkles } from 'lucide-react';\nimport { scrollToContact, scrollToPortfolio } from '../utils/navigation';\n\nconst Hero: React.FC = () => {\n  return (\n    <section id=\"hero\" className=\"min-h-screen relative overflow-hidden bg-gradient-to-br from-purple-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-purple-300 dark:bg-purple-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute top-40 right-10 w-72 h-72 bg-blue-300 dark:bg-blue-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute bottom-20 left-20 w-72 h-72 bg-pink-300 dark:bg-pink-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center min-h-screen\">\n          {/* Left Content */}\n          <div className=\"space-y-8\">\n            <div className=\"space-y-4\">\n              <div className=\"inline-flex items-center space-x-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full px-4 py-2 text-sm font-medium text-purple-600 dark:text-purple-400 border border-purple-200 dark:border-purple-800\">\n                <Sparkles className=\"h-4 w-4\" />\n                <span>Cutting-edge Technology Solutions</span>\n              </div>\n              \n              <h1 className=\"text-5xl lg:text-7xl font-bold leading-tight\">\n                <span className=\"bg-gradient-to-r from-purple-600 via-blue-600 to-purple-600 bg-clip-text text-transparent animate-gradient-x\">\n                  Beam Your\n                </span>\n                <br />\n                <span className=\"text-gray-900 dark:text-white\">\n                  Business to the\n                </span>\n                <br />\n                <span className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent animate-gradient-x\">\n                  Future\n                </span>\n              </h1>\n              \n              <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-2xl leading-relaxed\">\n                We craft exceptional web experiences, automate complex workflows, and harness AI to transform your business. Join the revolution of intelligent digital solutions.\n              </p>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <button\n                onClick={scrollToContact}\n                className=\"group relative inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\"\n              >\n                <span>Start Your Project</span>\n                <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200\" />\n              </button>\n\n              <button\n                onClick={scrollToPortfolio}\n                className=\"inline-flex items-center justify-center px-8 py-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-900 dark:text-white font-semibold rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-white dark:hover:bg-gray-800 transform hover:-translate-y-1 transition-all duration-300\"\n              >\n                View Our Work\n              </button>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 pt-8\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600 dark:text-purple-400\">500+</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Projects Delivered</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600 dark:text-blue-400\">98%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Client Satisfaction</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600 dark:text-purple-400\">24/7</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Support Available</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content - Floating Cards */}\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 rounded-3xl transform rotate-6 opacity-20\"></div>\n            \n            <div className=\"relative grid grid-cols-1 gap-6\">\n              {/* Web Design Card */}\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl transform hover:-translate-y-2 transition-all duration-300 border border-white/20 dark:border-gray-700/20\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg\">\n                    <Code className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 dark:text-white\">Web Design</h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">Modern, responsive interfaces</p>\n                  </div>\n                </div>\n              </div>\n\n              {/* AI Solutions Card */}\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl transform hover:-translate-y-2 transition-all duration-300 border border-white/20 dark:border-gray-700/20 ml-8\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg\">\n                    <Bot className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 dark:text-white\">AI Solutions</h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">Intelligent automation tools</p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Automation Card */}\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl transform hover:-translate-y-2 transition-all duration-300 border border-white/20 dark:border-gray-700/20\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg\">\n                    <Sparkles className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 dark:text-white\">Automation</h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">Streamlined workflows</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;"}