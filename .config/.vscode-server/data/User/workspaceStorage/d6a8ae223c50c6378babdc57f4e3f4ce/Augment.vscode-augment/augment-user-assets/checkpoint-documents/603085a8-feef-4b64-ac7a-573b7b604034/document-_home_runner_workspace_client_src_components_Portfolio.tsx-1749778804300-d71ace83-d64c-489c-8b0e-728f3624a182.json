{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Portfolio.tsx"}, "originalCode": "import React, { useState } from 'react';\nimport { ExternalLink, Filter, Code, Bot, Zap } from 'lucide-react';\nimport { scrollToContact } from '../utils/navigation';\n\nconst Portfolio: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState('all');\n\n  const projects = [\n    {\n      id: '1',\n      title: 'E-commerce Platform',\n      description: 'Modern e-commerce solution with AI-powered recommendations',\n      category: 'web-design',\n      image: 'https://images.pexels.com/photos/230544/pexels-photo-230544.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['React', 'Node.js', 'AI/ML', 'MongoDB'],\n      results: ['300% increase in conversions', '50% faster load times', '99.9% uptime'],\n      url: 'https://example.com'\n    },\n    {\n      id: '2',\n      title: 'Smart Workflow Automation',\n      description: 'Automated document processing system using AI',\n      category: 'automation',\n      image: 'https://images.pexels.com/photos/518244/pexels-photo-518244.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['Python', 'TensorFlow', 'AWS', 'Docker'],\n      results: ['80% time reduction', '95% accuracy rate', '$50K annual savings'],\n      url: 'https://example.com'\n    },\n    {\n      id: '3',\n      title: 'AI Chatbot Platform',\n      description: 'Intelligent customer service chatbot with NLP',\n      category: 'ai',\n      image: 'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['Python', 'OpenAI', 'React', 'FastAPI'],\n      results: ['90% customer satisfaction', '60% cost reduction', '24/7 availability'],\n      url: 'https://example.com'\n    },\n    {\n      id: '4',\n      title: 'Corporate Website Redesign',\n      description: 'Complete brand transformation with modern design',\n      category: 'web-design',\n      image: 'https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['React', 'Tailwind CSS', 'Framer Motion', 'Vercel'],\n      results: ['200% increase in engagement', '40% better conversion', 'Award-winning design'],\n      url: 'https://example.com'\n    },\n    {\n      id: '5',\n      title: 'Data Pipeline Automation',\n      description: 'Automated data processing and analytics pipeline',\n      category: 'automation',\n      image: 'https://images.pexels.com/photos/590020/pexels-photo-590020.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['Apache Airflow', 'Python', 'PostgreSQL', 'Tableau'],\n      results: ['Real-time processing', '99% data accuracy', 'Scalable architecture'],\n      url: 'https://example.com'\n    },\n    {\n      id: '6',\n      title: 'Predictive Analytics Dashboard',\n      description: 'AI-powered business intelligence dashboard',\n      category: 'ai',\n      image: 'https://images.pexels.com/photos/265087/pexels-photo-265087.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['Python', 'Scikit-learn', 'D3.js', 'Flask'],\n      results: ['85% prediction accuracy', 'Real-time insights', 'Improved decision making'],\n      url: 'https://example.com'\n    }\n  ];\n\n  const filters = [\n    { id: 'all', label: 'All Projects', icon: Filter },\n    { id: 'web-design', label: 'Web Design', icon: Code },\n    { id: 'automation', label: 'Automation', icon: Zap },\n    { id: 'ai', label: 'AI Solutions', icon: Bot }\n  ];\n\n  const filteredProjects = activeFilter === 'all' \n    ? projects \n    : projects.filter(project => project.category === activeFilter);\n\n  return (\n    <section id=\"portfolio\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Portfolio</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            Discover how we've helped businesses achieve remarkable results\n          </p>\n        </div>\n\n        {/* Filter Buttons */}\n        <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n          {filters.map((filter) => {\n            const IconComponent = filter.icon;\n            return (\n              <button\n                key={filter.id}\n                onClick={() => setActiveFilter(filter.id)}\n                className={`inline-flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all duration-200 ${\n                  activeFilter === filter.id\n                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'\n                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'\n                }`}\n              >\n                <IconComponent className=\"h-4 w-4\" />\n                <span>{filter.label}</span>\n              </button>\n            );\n          })}\n        </div>\n\n        {/* Projects Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {filteredProjects.map((project) => (\n            <div\n              key={project.id}\n              className=\"group bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2\"\n            >\n              {/* Project Image */}\n              <div className=\"relative overflow-hidden\">\n                <img\n                  src={project.image}\n                  alt={project.title}\n                  className=\"w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <button className=\"p-2 bg-white/90 rounded-full text-gray-900 hover:bg-white transition-colors duration-200\">\n                    <ExternalLink className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Project Content */}\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {project.title}\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\">\n                    {project.description}\n                  </p>\n                </div>\n\n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {project.technologies.map((tech, index) => (\n                    <span\n                      key={index}\n                      className=\"px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded-full\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                </div>\n\n                {/* Results */}\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white text-sm\">Key Results:</h4>\n                  {project.results.map((result, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <div className=\"w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full\"></div>\n                      <span className=\"text-gray-600 dark:text-gray-300 text-sm\">{result}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 mb-8\">\n            Ready to create your success story?\n          </p>\n          <button className=\"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\">\n            Start Your Project\n            <ExternalLink className=\"ml-2 h-5 w-5\" />\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Portfolio;", "modifiedCode": "import React, { useState } from 'react';\nimport { ExternalLink, Filter, Code, Bot, Zap } from 'lucide-react';\nimport { scrollToContact } from '../utils/navigation';\n\nconst Portfolio: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState('all');\n\n  const projects = [\n    {\n      id: '1',\n      title: 'E-commerce Platform',\n      description: 'Modern e-commerce solution with AI-powered recommendations',\n      category: 'web-design',\n      image: 'https://images.pexels.com/photos/230544/pexels-photo-230544.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['React', 'Node.js', 'AI/ML', 'MongoDB'],\n      results: ['300% increase in conversions', '50% faster load times', '99.9% uptime'],\n      url: 'https://example.com'\n    },\n    {\n      id: '2',\n      title: 'Smart Workflow Automation',\n      description: 'Automated document processing system using AI',\n      category: 'automation',\n      image: 'https://images.pexels.com/photos/518244/pexels-photo-518244.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['Python', 'TensorFlow', 'AWS', 'Docker'],\n      results: ['80% time reduction', '95% accuracy rate', '$50K annual savings'],\n      url: 'https://example.com'\n    },\n    {\n      id: '3',\n      title: 'AI Chatbot Platform',\n      description: 'Intelligent customer service chatbot with NLP',\n      category: 'ai',\n      image: 'https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['Python', 'OpenAI', 'React', 'FastAPI'],\n      results: ['90% customer satisfaction', '60% cost reduction', '24/7 availability'],\n      url: 'https://example.com'\n    },\n    {\n      id: '4',\n      title: 'Corporate Website Redesign',\n      description: 'Complete brand transformation with modern design',\n      category: 'web-design',\n      image: 'https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['React', 'Tailwind CSS', 'Framer Motion', 'Vercel'],\n      results: ['200% increase in engagement', '40% better conversion', 'Award-winning design'],\n      url: 'https://example.com'\n    },\n    {\n      id: '5',\n      title: 'Data Pipeline Automation',\n      description: 'Automated data processing and analytics pipeline',\n      category: 'automation',\n      image: 'https://images.pexels.com/photos/590020/pexels-photo-590020.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['Apache Airflow', 'Python', 'PostgreSQL', 'Tableau'],\n      results: ['Real-time processing', '99% data accuracy', 'Scalable architecture'],\n      url: 'https://example.com'\n    },\n    {\n      id: '6',\n      title: 'Predictive Analytics Dashboard',\n      description: 'AI-powered business intelligence dashboard',\n      category: 'ai',\n      image: 'https://images.pexels.com/photos/265087/pexels-photo-265087.jpeg?auto=compress&cs=tinysrgb&w=800',\n      technologies: ['Python', 'Scikit-learn', 'D3.js', 'Flask'],\n      results: ['85% prediction accuracy', 'Real-time insights', 'Improved decision making'],\n      url: 'https://example.com'\n    }\n  ];\n\n  const filters = [\n    { id: 'all', label: 'All Projects', icon: Filter },\n    { id: 'web-design', label: 'Web Design', icon: Code },\n    { id: 'automation', label: 'Automation', icon: Zap },\n    { id: 'ai', label: 'AI Solutions', icon: Bot }\n  ];\n\n  const filteredProjects = activeFilter === 'all' \n    ? projects \n    : projects.filter(project => project.category === activeFilter);\n\n  return (\n    <section id=\"portfolio\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Portfolio</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            Discover how we've helped businesses achieve remarkable results\n          </p>\n        </div>\n\n        {/* Filter Buttons */}\n        <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n          {filters.map((filter) => {\n            const IconComponent = filter.icon;\n            return (\n              <button\n                key={filter.id}\n                onClick={() => setActiveFilter(filter.id)}\n                className={`inline-flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all duration-200 ${\n                  activeFilter === filter.id\n                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'\n                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'\n                }`}\n              >\n                <IconComponent className=\"h-4 w-4\" />\n                <span>{filter.label}</span>\n              </button>\n            );\n          })}\n        </div>\n\n        {/* Projects Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {filteredProjects.map((project) => (\n            <div\n              key={project.id}\n              className=\"group bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2\"\n            >\n              {/* Project Image */}\n              <div className=\"relative overflow-hidden\">\n                <img\n                  src={project.image}\n                  alt={project.title}\n                  className=\"w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                <div className=\"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <button className=\"p-2 bg-white/90 rounded-full text-gray-900 hover:bg-white transition-colors duration-200\">\n                    <ExternalLink className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Project Content */}\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {project.title}\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\">\n                    {project.description}\n                  </p>\n                </div>\n\n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {project.technologies.map((tech, index) => (\n                    <span\n                      key={index}\n                      className=\"px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded-full\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                </div>\n\n                {/* Results */}\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white text-sm\">Key Results:</h4>\n                  {project.results.map((result, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <div className=\"w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full\"></div>\n                      <span className=\"text-gray-600 dark:text-gray-300 text-sm\">{result}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 mb-8\">\n            Ready to create your success story?\n          </p>\n          <button className=\"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\">\n            Start Your Project\n            <ExternalLink className=\"ml-2 h-5 w-5\" />\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Portfolio;"}