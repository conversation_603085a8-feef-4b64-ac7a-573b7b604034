{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Testimonials.tsx"}, "originalCode": "import React from 'react';\nimport { <PERSON>, Quote, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { scrollToContact } from '../utils/navigation';\n\nconst Testimonials: React.FC = () => {\n  const testimonials = [\n    {\n      id: '1',\n      name: '<PERSON>',\n      role: 'CEO',\n      company: 'TechFlow Solutions',\n      content: 'Beam.tech transformed our entire digital presence. Their AI-powered automation solution reduced our processing time by 80% and increased customer satisfaction dramatically. The team\\'s expertise in modern web technologies is unmatched.',\n      image: 'https://images.pexels.com/photos/3756679/pexels-photo-3756679.jpeg?auto=compress&cs=tinysrgb&w=150',\n      rating: 5\n    },\n    {\n      id: '2',\n      name: '<PERSON>',\n      role: 'CTO',\n      company: 'InnovateX',\n      content: 'Working with Beam.tech was a game-changer. They delivered a stunning website that not only looks amazing but performs exceptionally. The integration of AI features has given us a competitive edge in our market.',\n      image: 'https://images.pexels.com/photos/3778212/pexels-photo-3778212.jpeg?auto=compress&cs=tinysrgb&w=150',\n      rating: 5\n    },\n    {\n      id: '3',\n      name: '<PERSON>',\n      role: 'Founder',\n      company: 'GreenTech Innovations',\n      content: 'The automation workflows Beam.tech implemented for us are incredible. What used to take our team hours now happens automatically. Their attention to detail and commitment to excellence is remarkable.',\n      image: 'https://images.pexels.com/photos/3756679/pexels-photo-3756679.jpeg?auto=compress&cs=tinysrgb&w=150',\n      rating: 5\n    },\n    {\n      id: '4',\n      name: 'David Kim',\n      role: 'Director of Operations',\n      company: 'DataDriven Corp',\n      content: 'Beam.tech\\'s AI solutions have revolutionized how we handle data processing. The predictive analytics dashboard they built provides insights we never had before. ROI was achieved within the first quarter.',\n      image: 'https://images.pexels.com/photos/3778212/pexels-photo-3778212.jpeg?auto=compress&cs=tinysrgb&w=150',\n      rating: 5\n    }\n  ];\n\n  const [currentIndex, setCurrentIndex] = React.useState(0);\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);\n  };\n\n  return (\n    <section id=\"testimonials\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            What Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Clients Say</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            Don't just take our word for it. Here's what industry leaders say about working with us.\n          </p>\n        </div>\n\n        {/* Main Testimonial */}\n        <div className=\"relative max-w-4xl mx-auto mb-16\">\n          <div className=\"bg-white dark:bg-gray-900 rounded-2xl p-8 lg:p-12 shadow-xl\">\n            {/* Quote Icon */}\n            <div className=\"absolute -top-6 left-8\">\n              <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 rounded-full p-4\">\n                <Quote className=\"h-8 w-8 text-white\" />\n              </div>\n            </div>\n\n            {/* Testimonial Content */}\n            <div className=\"pt-8\">\n              <div className=\"flex items-center mb-6\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"h-6 w-6 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n\n              <blockquote className=\"text-xl lg:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8\">\n                \"{testimonials[currentIndex].content}\"\n              </blockquote>\n\n              <div className=\"flex items-center space-x-4\">\n                <img\n                  src={testimonials[currentIndex].image}\n                  alt={testimonials[currentIndex].name}\n                  className=\"w-16 h-16 rounded-full object-cover\"\n                />\n                <div>\n                  <div className=\"font-bold text-gray-900 dark:text-white text-lg\">\n                    {testimonials[currentIndex].name}\n                  </div>\n                  <div className=\"text-gray-600 dark:text-gray-400\">\n                    {testimonials[currentIndex].role} at {testimonials[currentIndex].company}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"absolute top-1/2 -translate-y-1/2 -left-6\">\n              <button\n                onClick={prevTestimonial}\n                className=\"p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-all duration-200\"\n              >\n                <ChevronLeft className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"absolute top-1/2 -translate-y-1/2 -right-6\">\n              <button\n                onClick={nextTestimonial}\n                className=\"p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-all duration-200\"\n              >\n                <ChevronRight className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center space-x-2 mt-8\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentIndex(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                  index === currentIndex\n                    ? 'bg-gradient-to-r from-purple-600 to-blue-600'\n                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2\">98%</div>\n            <div className=\"text-gray-600 dark:text-gray-300\">Client Satisfaction Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2\">500+</div>\n            <div className=\"text-gray-600 dark:text-gray-300\">Successful Projects</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2\">50+</div>\n            <div className=\"text-gray-600 dark:text-gray-300\">Industry Awards</div>\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 mb-8\">\n            Ready to join our satisfied clients?\n          </p>\n          <button className=\"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\">\n            Get Your Free Consultation\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Testimonials;", "modifiedCode": "import React from 'react';\nimport { <PERSON>, Quote, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { scrollToContact } from '../utils/navigation';\n\nconst Testimonials: React.FC = () => {\n  const testimonials = [\n    {\n      id: '1',\n      name: '<PERSON>',\n      role: 'CEO',\n      company: 'TechFlow Solutions',\n      content: 'Beam.tech transformed our entire digital presence. Their AI-powered automation solution reduced our processing time by 80% and increased customer satisfaction dramatically. The team\\'s expertise in modern web technologies is unmatched.',\n      image: 'https://images.pexels.com/photos/3756679/pexels-photo-3756679.jpeg?auto=compress&cs=tinysrgb&w=150',\n      rating: 5\n    },\n    {\n      id: '2',\n      name: '<PERSON>',\n      role: 'CTO',\n      company: 'InnovateX',\n      content: 'Working with Beam.tech was a game-changer. They delivered a stunning website that not only looks amazing but performs exceptionally. The integration of AI features has given us a competitive edge in our market.',\n      image: 'https://images.pexels.com/photos/3778212/pexels-photo-3778212.jpeg?auto=compress&cs=tinysrgb&w=150',\n      rating: 5\n    },\n    {\n      id: '3',\n      name: '<PERSON>',\n      role: 'Founder',\n      company: 'GreenTech Innovations',\n      content: 'The automation workflows Beam.tech implemented for us are incredible. What used to take our team hours now happens automatically. Their attention to detail and commitment to excellence is remarkable.',\n      image: 'https://images.pexels.com/photos/3756679/pexels-photo-3756679.jpeg?auto=compress&cs=tinysrgb&w=150',\n      rating: 5\n    },\n    {\n      id: '4',\n      name: 'David Kim',\n      role: 'Director of Operations',\n      company: 'DataDriven Corp',\n      content: 'Beam.tech\\'s AI solutions have revolutionized how we handle data processing. The predictive analytics dashboard they built provides insights we never had before. ROI was achieved within the first quarter.',\n      image: 'https://images.pexels.com/photos/3778212/pexels-photo-3778212.jpeg?auto=compress&cs=tinysrgb&w=150',\n      rating: 5\n    }\n  ];\n\n  const [currentIndex, setCurrentIndex] = React.useState(0);\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);\n  };\n\n  return (\n    <section id=\"testimonials\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            What Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Clients Say</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            Don't just take our word for it. Here's what industry leaders say about working with us.\n          </p>\n        </div>\n\n        {/* Main Testimonial */}\n        <div className=\"relative max-w-4xl mx-auto mb-16\">\n          <div className=\"bg-white dark:bg-gray-900 rounded-2xl p-8 lg:p-12 shadow-xl\">\n            {/* Quote Icon */}\n            <div className=\"absolute -top-6 left-8\">\n              <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 rounded-full p-4\">\n                <Quote className=\"h-8 w-8 text-white\" />\n              </div>\n            </div>\n\n            {/* Testimonial Content */}\n            <div className=\"pt-8\">\n              <div className=\"flex items-center mb-6\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"h-6 w-6 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n\n              <blockquote className=\"text-xl lg:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8\">\n                \"{testimonials[currentIndex].content}\"\n              </blockquote>\n\n              <div className=\"flex items-center space-x-4\">\n                <img\n                  src={testimonials[currentIndex].image}\n                  alt={testimonials[currentIndex].name}\n                  className=\"w-16 h-16 rounded-full object-cover\"\n                />\n                <div>\n                  <div className=\"font-bold text-gray-900 dark:text-white text-lg\">\n                    {testimonials[currentIndex].name}\n                  </div>\n                  <div className=\"text-gray-600 dark:text-gray-400\">\n                    {testimonials[currentIndex].role} at {testimonials[currentIndex].company}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"absolute top-1/2 -translate-y-1/2 -left-6\">\n              <button\n                onClick={prevTestimonial}\n                className=\"p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-all duration-200\"\n              >\n                <ChevronLeft className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"absolute top-1/2 -translate-y-1/2 -right-6\">\n              <button\n                onClick={nextTestimonial}\n                className=\"p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-all duration-200\"\n              >\n                <ChevronRight className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center space-x-2 mt-8\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentIndex(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                  index === currentIndex\n                    ? 'bg-gradient-to-r from-purple-600 to-blue-600'\n                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2\">98%</div>\n            <div className=\"text-gray-600 dark:text-gray-300\">Client Satisfaction Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2\">500+</div>\n            <div className=\"text-gray-600 dark:text-gray-300\">Successful Projects</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2\">50+</div>\n            <div className=\"text-gray-600 dark:text-gray-300\">Industry Awards</div>\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 mb-8\">\n            Ready to join our satisfied clients?\n          </p>\n          <button className=\"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\">\n            Get Your Free Consultation\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Testimonials;"}