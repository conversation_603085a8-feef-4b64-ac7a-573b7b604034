{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useForm.ts"}, "modifiedCode": "import { useState, useCallback } from 'react';\n\nexport interface UseFormOptions<T> {\n  initialValues: T;\n  validate?: (values: T) => Record<string, string>;\n  onSubmit: (values: T) => Promise<{ success: boolean; message: string }>;\n}\n\nexport interface UseFormReturn<T> {\n  values: T;\n  errors: Record<string, string>;\n  isSubmitting: boolean;\n  isSubmitted: boolean;\n  submitMessage: string;\n  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\n  handleSubmit: (e: React.FormEvent) => Promise<void>;\n  resetForm: () => void;\n  setFieldValue: (field: keyof T, value: any) => void;\n  setFieldError: (field: string, error: string) => void;\n  clearErrors: () => void;\n}\n\nexport const useForm = <T extends Record<string, any>>({\n  initialValues,\n  validate,\n  onSubmit\n}: UseFormOptions<T>): UseFormReturn<T> => {\n  const [values, setValues] = useState<T>(initialValues);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [submitMessage, setSubmitMessage] = useState('');\n\n  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setValues(prev => ({ ...prev, [name]: value }));\n    \n    // Clear error for this field when user starts typing\n    if (errors[name]) {\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[name];\n        return newErrors;\n      });\n    }\n  }, [errors]);\n\n  const setFieldValue = useCallback((field: keyof T, value: any) => {\n    setValues(prev => ({ ...prev, [field]: value }));\n  }, []);\n\n  const setFieldError = useCallback((field: string, error: string) => {\n    setErrors(prev => ({ ...prev, [field]: error }));\n  }, []);\n\n  const clearErrors = useCallback(() => {\n    setErrors({});\n  }, []);\n\n  const resetForm = useCallback(() => {\n    setValues(initialValues);\n    setErrors({});\n    setIsSubmitting(false);\n    setIsSubmitted(false);\n    setSubmitMessage('');\n  }, [initialValues]);\n\n  const handleSubmit = useCallback(async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Clear previous errors and messages\n    setErrors({});\n    setSubmitMessage('');\n    \n    // Validate form if validator is provided\n    if (validate) {\n      const validationErrors = validate(values);\n      if (Object.keys(validationErrors).length > 0) {\n        setErrors(validationErrors);\n        return;\n      }\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const result = await onSubmit(values);\n      \n      if (result.success) {\n        setIsSubmitted(true);\n        setSubmitMessage(result.message);\n        \n        // Reset form after successful submission (optional)\n        setTimeout(() => {\n          resetForm();\n        }, 3000);\n      } else {\n        setSubmitMessage(result.message);\n      }\n    } catch (error) {\n      console.error('Form submission error:', error);\n      setSubmitMessage('An unexpected error occurred. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [values, validate, onSubmit, resetForm]);\n\n  return {\n    values,\n    errors,\n    isSubmitting,\n    isSubmitted,\n    submitMessage,\n    handleChange,\n    handleSubmit,\n    resetForm,\n    setFieldValue,\n    setFieldError,\n    clearErrors\n  };\n};\n"}