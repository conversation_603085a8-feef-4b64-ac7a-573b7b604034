{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/navigation.ts"}, "modifiedCode": "// Navigation and scrolling utilities\n\nexport const smoothScrollToElement = (elementId: string, offset: number = 80): void => {\n  const element = document.getElementById(elementId.replace('#', ''));\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n};\n\nexport const scrollToTop = (): void => {\n  window.scrollTo({\n    top: 0,\n    behavior: 'smooth'\n  });\n};\n\nexport const scrollToContact = (): void => {\n  smoothScrollToElement('contact');\n};\n\nexport const scrollToPortfolio = (): void => {\n  smoothScrollToElement('portfolio');\n};\n\nexport const scrollToServices = (): void => {\n  smoothScrollToElement('services');\n};\n\n// Handle navigation link clicks with smooth scrolling\nexport const handleNavClick = (href: string, callback?: () => void): void => {\n  if (href.startsWith('#')) {\n    smoothScrollToElement(href);\n    callback?.();\n  } else {\n    // Handle external links\n    window.open(href, '_blank', 'noopener,noreferrer');\n  }\n};\n"}