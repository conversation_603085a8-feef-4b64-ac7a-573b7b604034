{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/csvLogger.ts"}, "originalCode": "import fs from 'fs';\nimport path from 'path';\n\n// CSV logging utility for contact forms and newsletter subscriptions\n\nexport interface ContactFormEntry {\n  timestamp: string;\n  name: string;\n  email: string;\n  company: string;\n  project_type: string;\n  budget: string;\n  message: string;\n  submission_status: 'success' | 'failed';\n}\n\nexport interface NewsletterEntry {\n  timestamp: string;\n  email: string;\n  subscription_status: 'success' | 'failed';\n}\n\n// Escape CSV values to prevent corruption from commas, quotes, and newlines\nfunction escapeCsvValue(value: string): string {\n  if (!value) return '';\n  \n  // Convert to string and handle null/undefined\n  const stringValue = String(value);\n  \n  // If the value contains comma, quote, or newline, wrap in quotes and escape internal quotes\n  if (stringValue.includes(',') || stringValue.includes('\"') || stringValue.includes('\\n') || stringValue.includes('\\r')) {\n    return `\"${stringValue.replace(/\"/g, '\"\"')}\"`;\n  }\n  \n  return stringValue;\n}\n\n// Convert object to CSV row\nfunction objectToCsvRow(obj: Record<string, any>): string {\n  return Object.values(obj).map(value => escapeCsvValue(String(value))).join(',');\n}\n\n// Ensure CSV file exists with headers\nasync function ensureCsvFile(filePath: string, headers: string[]): Promise<void> {\n  try {\n    // Check if file exists\n    await fs.promises.access(filePath);\n  } catch (error) {\n    // File doesn't exist, create it with headers\n    const headerRow = headers.map(header => escapeCsvValue(header)).join(',');\n    await fs.promises.writeFile(filePath, headerRow + '\\n', 'utf8');\n    console.log(`Created CSV file: ${filePath}`);\n  }\n}\n\n// Append data to CSV file\nasync function appendToCsv(filePath: string, data: string): Promise<void> {\n  try {\n    await fs.promises.appendFile(filePath, data + '\\n', 'utf8');\n  } catch (error) {\n    console.error(`Error appending to CSV file ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// Log contact form submission\nexport async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {\n  try {\n    const csvDir = path.resolve(process.cwd(), 'server', 'logs');\n    const csvFile = path.join(csvDir, 'contact-enquiries.csv');\n    \n    // Ensure logs directory exists\n    await fs.promises.mkdir(csvDir, { recursive: true });\n    \n    // Define headers for contact form CSV\n    const headers = [\n      'timestamp',\n      'name', \n      'email',\n      'company',\n      'project_type',\n      'budget',\n      'message',\n      'submission_status'\n    ];\n    \n    // Ensure CSV file exists with headers\n    await ensureCsvFile(csvFile, headers);\n    \n    // Convert entry to CSV row\n    const csvRow = objectToCsvRow(entry);\n    \n    // Append to file\n    await appendToCsv(csvFile, csvRow);\n    \n    console.log(`Contact form submission logged: ${entry.email}`);\n  } catch (error) {\n    console.error('Error logging contact form submission:', error);\n    // Don't throw error to avoid breaking the main flow\n  }\n}\n\n// Log newsletter subscription\nexport async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {\n  try {\n    const csvDir = path.resolve(process.cwd(), 'server', 'logs');\n    const csvFile = path.join(csvDir, 'newsletter-subscriptions.csv');\n    \n    // Ensure logs directory exists\n    await fs.promises.mkdir(csvDir, { recursive: true });\n    \n    // Define headers for newsletter CSV\n    const headers = [\n      'timestamp',\n      'email',\n      'subscription_status'\n    ];\n    \n    // Ensure CSV file exists with headers\n    await ensureCsvFile(csvFile, headers);\n    \n    // Convert entry to CSV row\n    const csvRow = objectToCsvRow(entry);\n    \n    // Append to file\n    await appendToCsv(csvFile, csvRow);\n    \n    console.log(`Newsletter subscription logged: ${entry.email}`);\n  } catch (error) {\n    console.error('Error logging newsletter subscription:', error);\n    // Don't throw error to avoid breaking the main flow\n  }\n}\n\n// Helper function to get current ISO timestamp\nexport function getCurrentTimestamp(): string {\n  return new Date().toISOString();\n}\n\n// Helper function to create contact form entry\nexport function createContactFormEntry(\n  name: string,\n  email: string,\n  company: string,\n  project: string,\n  budget: string,\n  message: string,\n  status: 'success' | 'failed'\n): ContactFormEntry {\n  return {\n    timestamp: getCurrentTimestamp(),\n    name: name || '',\n    email: email || '',\n    company: company || '',\n    project_type: project || '',\n    budget: budget || '',\n    message: message || '',\n    submission_status: status\n  };\n}\n\n// Helper function to create newsletter entry\nexport function createNewsletterEntry(\n  email: string,\n  status: 'success' | 'failed'\n): NewsletterEntry {\n  return {\n    timestamp: getCurrentTimestamp(),\n    email: email || '',\n    subscription_status: status\n  };\n}\n", "modifiedCode": "import fs from 'fs';\nimport path from 'path';\n\n// CSV logging utility for contact forms and newsletter subscriptions\n\nexport interface ContactFormEntry {\n  timestamp: string;\n  name: string;\n  email: string;\n  company: string;\n  project_type: string;\n  budget: string;\n  message: string;\n  submission_status: 'success' | 'failed';\n}\n\nexport interface NewsletterEntry {\n  timestamp: string;\n  email: string;\n  subscription_status: 'success' | 'failed';\n}\n\n// Escape CSV values to prevent corruption from commas, quotes, and newlines\nfunction escapeCsvValue(value: string): string {\n  if (!value) return '';\n  \n  // Convert to string and handle null/undefined\n  const stringValue = String(value);\n  \n  // If the value contains comma, quote, or newline, wrap in quotes and escape internal quotes\n  if (stringValue.includes(',') || stringValue.includes('\"') || stringValue.includes('\\n') || stringValue.includes('\\r')) {\n    return `\"${stringValue.replace(/\"/g, '\"\"')}\"`;\n  }\n  \n  return stringValue;\n}\n\n// Convert object to CSV row\nfunction objectToCsvRow(obj: Record<string, any>): string {\n  return Object.values(obj).map(value => escapeCsvValue(String(value))).join(',');\n}\n\n// Ensure CSV file exists with headers\nasync function ensureCsvFile(filePath: string, headers: string[]): Promise<void> {\n  try {\n    // Check if file exists\n    await fs.promises.access(filePath);\n  } catch (error) {\n    // File doesn't exist, create it with headers\n    const headerRow = headers.map(header => escapeCsvValue(header)).join(',');\n    await fs.promises.writeFile(filePath, headerRow + '\\n', 'utf8');\n    console.log(`Created CSV file: ${filePath}`);\n  }\n}\n\n// Append data to CSV file\nasync function appendToCsv(filePath: string, data: string): Promise<void> {\n  try {\n    await fs.promises.appendFile(filePath, data + '\\n', 'utf8');\n  } catch (error) {\n    console.error(`Error appending to CSV file ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// Log contact form submission\nexport async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {\n  try {\n    // Use __dirname equivalent for ES modules\n    const serverDir = path.dirname(new URL(import.meta.url).pathname);\n    const csvDir = path.join(serverDir, 'logs');\n    const csvFile = path.join(csvDir, 'contact-enquiries.csv');\n    \n    // Ensure logs directory exists\n    await fs.promises.mkdir(csvDir, { recursive: true });\n    \n    // Define headers for contact form CSV\n    const headers = [\n      'timestamp',\n      'name', \n      'email',\n      'company',\n      'project_type',\n      'budget',\n      'message',\n      'submission_status'\n    ];\n    \n    // Ensure CSV file exists with headers\n    await ensureCsvFile(csvFile, headers);\n    \n    // Convert entry to CSV row\n    const csvRow = objectToCsvRow(entry);\n    \n    // Append to file\n    await appendToCsv(csvFile, csvRow);\n    \n    console.log(`Contact form submission logged: ${entry.email}`);\n  } catch (error) {\n    console.error('Error logging contact form submission:', error);\n    // Don't throw error to avoid breaking the main flow\n  }\n}\n\n// Log newsletter subscription\nexport async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {\n  try {\n    const csvDir = path.resolve(process.cwd(), 'server', 'logs');\n    const csvFile = path.join(csvDir, 'newsletter-subscriptions.csv');\n    \n    // Ensure logs directory exists\n    await fs.promises.mkdir(csvDir, { recursive: true });\n    \n    // Define headers for newsletter CSV\n    const headers = [\n      'timestamp',\n      'email',\n      'subscription_status'\n    ];\n    \n    // Ensure CSV file exists with headers\n    await ensureCsvFile(csvFile, headers);\n    \n    // Convert entry to CSV row\n    const csvRow = objectToCsvRow(entry);\n    \n    // Append to file\n    await appendToCsv(csvFile, csvRow);\n    \n    console.log(`Newsletter subscription logged: ${entry.email}`);\n  } catch (error) {\n    console.error('Error logging newsletter subscription:', error);\n    // Don't throw error to avoid breaking the main flow\n  }\n}\n\n// Helper function to get current ISO timestamp\nexport function getCurrentTimestamp(): string {\n  return new Date().toISOString();\n}\n\n// Helper function to create contact form entry\nexport function createContactFormEntry(\n  name: string,\n  email: string,\n  company: string,\n  project: string,\n  budget: string,\n  message: string,\n  status: 'success' | 'failed'\n): ContactFormEntry {\n  return {\n    timestamp: getCurrentTimestamp(),\n    name: name || '',\n    email: email || '',\n    company: company || '',\n    project_type: project || '',\n    budget: budget || '',\n    message: message || '',\n    submission_status: status\n  };\n}\n\n// Helper function to create newsletter entry\nexport function createNewsletterEntry(\n  email: string,\n  status: 'success' | 'failed'\n): NewsletterEntry {\n  return {\n    timestamp: getCurrentTimestamp(),\n    email: email || '',\n    subscription_status: status\n  };\n}\n"}