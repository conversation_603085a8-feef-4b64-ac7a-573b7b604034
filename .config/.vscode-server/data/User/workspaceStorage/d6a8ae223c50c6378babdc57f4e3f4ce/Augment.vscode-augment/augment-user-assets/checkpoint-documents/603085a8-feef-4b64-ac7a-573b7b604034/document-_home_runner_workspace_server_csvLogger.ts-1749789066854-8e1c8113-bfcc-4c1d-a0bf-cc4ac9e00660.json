{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/csvLogger.ts"}, "originalCode": "import fs from 'fs';\nimport path from 'path';\n\n// Simple text logging utility for contact forms and newsletter subscriptions\n\nexport interface ContactFormEntry {\n  timestamp: string;\n  name: string;\n  email: string;\n  company: string;\n  project_type: string;\n  budget: string;\n  message: string;\n  submission_status: 'success' | 'failed';\n}\n\nexport interface NewsletterEntry {\n  timestamp: string;\n  email: string;\n  subscription_status: 'success' | 'failed';\n}\n\n// Format contact form entry as text line\nfunction formatContactFormEntry(entry: ContactFormEntry): string {\n  const cleanMessage = entry.message.replace(/\\n/g, ' ').replace(/\\r/g, ' ').substring(0, 200);\n  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;\n}\n\n// Format newsletter entry as text line\nfunction formatNewsletterEntry(entry: NewsletterEntry): string {\n  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;\n}\n\n// Ensure log directory exists\nasync function ensureLogDirectory(logDir: string): Promise<void> {\n  try {\n    await fs.promises.mkdir(logDir, { recursive: true });\n    console.log(`📁 Log directory ensured: ${logDir}`);\n  } catch (error) {\n    console.error(`❌ Error creating log directory ${logDir}:`, error);\n    throw error;\n  }\n}\n\n// Append data to text file\nasync function appendToTextFile(filePath: string, data: string): Promise<void> {\n  try {\n    await fs.promises.appendFile(filePath, data + '\\n', 'utf8');\n    console.log(`📝 Successfully appended to: ${filePath}`);\n  } catch (error) {\n    console.error(`❌ Error appending to text file ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// Log contact form submission\nexport async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {\n  console.log(`🔍 DEBUG: logContactFormSubmission called for ${entry.email}`);\n\n  let textFile = '';\n  let logDir = '';\n\n  try {\n    // Use __dirname equivalent for ES modules\n    const serverDir = path.dirname(new URL(import.meta.url).pathname);\n    logDir = path.join(serverDir, 'logs');\n    textFile = path.join(logDir, 'contact-form-submissions.txt');\n\n    console.log(`🔍 DEBUG: Log directory: ${logDir}`);\n    console.log(`🔍 DEBUG: Text file path: ${textFile}`);\n\n    // Ensure logs directory exists\n    await ensureLogDirectory(logDir);\n\n    // Format entry as text line\n    const textLine = formatContactFormEntry(entry);\n    console.log(`🔍 DEBUG: Formatted text line: ${textLine}`);\n\n    // Append to file\n    await appendToTextFile(textFile, textLine);\n\n    console.log(`✅ Contact form submission logged: ${entry.email} to ${textFile}`);\n  } catch (error) {\n    console.error('❌ Error logging contact form submission:', error);\n    console.error('Text file path:', textFile);\n    console.error('Log directory:', logDir);\n    // Don't throw error to avoid breaking the main flow\n  }\n}\n\n// Log newsletter subscription\nexport async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {\n  console.log(`🔍 DEBUG: logNewsletterSubscription called for ${entry.email}`);\n\n  let textFile = '';\n  let logDir = '';\n\n  try {\n    // Use __dirname equivalent for ES modules\n    const serverDir = path.dirname(new URL(import.meta.url).pathname);\n    logDir = path.join(serverDir, 'logs');\n    textFile = path.join(logDir, 'newsletter-subscriptions.txt');\n\n    console.log(`🔍 DEBUG: Log directory: ${logDir}`);\n    console.log(`🔍 DEBUG: Text file path: ${textFile}`);\n\n    // Ensure logs directory exists\n    await ensureLogDirectory(logDir);\n\n    // Format entry as text line\n    const textLine = formatNewsletterEntry(entry);\n    console.log(`🔍 DEBUG: Formatted text line: ${textLine}`);\n\n    // Append to file\n    await appendToTextFile(textFile, textLine);\n\n    console.log(`✅ Newsletter subscription logged: ${entry.email} to ${textFile}`);\n  } catch (error) {\n    console.error('❌ Error logging newsletter subscription:', error);\n    console.error('Text file path:', textFile);\n    console.error('Log directory:', logDir);\n    // Don't throw error to avoid breaking the main flow\n  }\n}\n\n// Helper function to get current ISO timestamp\nexport function getCurrentTimestamp(): string {\n  return new Date().toISOString();\n}\n\n// Helper function to create contact form entry\nexport function createContactFormEntry(\n  name: string,\n  email: string,\n  company: string,\n  project: string,\n  budget: string,\n  message: string,\n  status: 'success' | 'failed'\n): ContactFormEntry {\n  return {\n    timestamp: getCurrentTimestamp(),\n    name: name || '',\n    email: email || '',\n    company: company || '',\n    project_type: project || '',\n    budget: budget || '',\n    message: message || '',\n    submission_status: status\n  };\n}\n\n// Helper function to create newsletter entry\nexport function createNewsletterEntry(\n  email: string,\n  status: 'success' | 'failed'\n): NewsletterEntry {\n  return {\n    timestamp: getCurrentTimestamp(),\n    email: email || '',\n    subscription_status: status\n  };\n}\n", "modifiedCode": "import fs from 'fs';\nimport path from 'path';\n\n// Simple text logging utility for contact forms and newsletter subscriptions\n\nexport interface ContactFormEntry {\n  timestamp: string;\n  name: string;\n  email: string;\n  company: string;\n  project_type: string;\n  budget: string;\n  message: string;\n  submission_status: 'success' | 'failed';\n}\n\nexport interface NewsletterEntry {\n  timestamp: string;\n  email: string;\n  subscription_status: 'success' | 'failed';\n}\n\n// Format contact form entry as text line\nfunction formatContactFormEntry(entry: ContactFormEntry): string {\n  const cleanMessage = entry.message.replace(/\\n/g, ' ').replace(/\\r/g, ' ').substring(0, 200);\n  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;\n}\n\n// Format newsletter entry as text line\nfunction formatNewsletterEntry(entry: NewsletterEntry): string {\n  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;\n}\n\n// Ensure log directory exists\nasync function ensureLogDirectory(logDir: string): Promise<void> {\n  try {\n    await fs.promises.mkdir(logDir, { recursive: true });\n    console.log(`📁 Log directory ensured: ${logDir}`);\n  } catch (error) {\n    console.error(`❌ Error creating log directory ${logDir}:`, error);\n    throw error;\n  }\n}\n\n// Append data to text file\nasync function appendToTextFile(filePath: string, data: string): Promise<void> {\n  try {\n    await fs.promises.appendFile(filePath, data + '\\n', 'utf8');\n    console.log(`📝 Successfully appended to: ${filePath}`);\n  } catch (error) {\n    console.error(`❌ Error appending to text file ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// Log contact form submission\nexport async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {\n  console.log(`🔍 DEBUG: logContactFormSubmission called for ${entry.email}`);\n\n  let textFile = '';\n  let logDir = '';\n\n  try {\n    // Use __dirname equivalent for ES modules\n    const serverDir = path.dirname(new URL(import.meta.url).pathname);\n    logDir = path.join(serverDir, 'logs');\n    textFile = path.join(logDir, 'contact-form-submissions.txt');\n\n    console.log(`🔍 DEBUG: Log directory: ${logDir}`);\n    console.log(`🔍 DEBUG: Text file path: ${textFile}`);\n\n    // Ensure logs directory exists\n    await ensureLogDirectory(logDir);\n\n    // Format entry as text line\n    const textLine = formatContactFormEntry(entry);\n    console.log(`🔍 DEBUG: Formatted text line: ${textLine}`);\n\n    // Append to file\n    await appendToTextFile(textFile, textLine);\n\n    console.log(`✅ Contact form submission logged: ${entry.email} to ${textFile}`);\n  } catch (error) {\n    console.error('❌ Error logging contact form submission:', error);\n    console.error('Text file path:', textFile);\n    console.error('Log directory:', logDir);\n    // Don't throw error to avoid breaking the main flow\n  }\n}\n\n// Log newsletter subscription\nexport async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {\n  console.log(`🔍 DEBUG: logNewsletterSubscription called for ${entry.email}`);\n\n  let textFile = '';\n  let logDir = '';\n\n  try {\n    // Use __dirname equivalent for ES modules\n    const serverDir = path.dirname(new URL(import.meta.url).pathname);\n    logDir = path.join(serverDir, 'logs');\n    textFile = path.join(logDir, 'newsletter-subscriptions.txt');\n\n    console.log(`🔍 DEBUG: Log directory: ${logDir}`);\n    console.log(`🔍 DEBUG: Text file path: ${textFile}`);\n\n    // Ensure logs directory exists\n    await ensureLogDirectory(logDir);\n\n    // Format entry as text line\n    const textLine = formatNewsletterEntry(entry);\n    console.log(`🔍 DEBUG: Formatted text line: ${textLine}`);\n\n    // Append to file\n    await appendToTextFile(textFile, textLine);\n\n    console.log(`✅ Newsletter subscription logged: ${entry.email} to ${textFile}`);\n  } catch (error) {\n    console.error('❌ Error logging newsletter subscription:', error);\n    console.error('Text file path:', textFile);\n    console.error('Log directory:', logDir);\n    // Don't throw error to avoid breaking the main flow\n  }\n}\n\n// Helper function to get current ISO timestamp\nexport function getCurrentTimestamp(): string {\n  return new Date().toISOString();\n}\n\n// Helper function to create contact form entry\nexport function createContactFormEntry(\n  name: string,\n  email: string,\n  company: string,\n  project: string,\n  budget: string,\n  message: string,\n  status: 'success' | 'failed'\n): ContactFormEntry {\n  console.log(`🔍 DEBUG: createContactFormEntry called for ${email} with status ${status}`);\n  return {\n    timestamp: getCurrentTimestamp(),\n    name: name || '',\n    email: email || '',\n    company: company || '',\n    project_type: project || '',\n    budget: budget || '',\n    message: message || '',\n    submission_status: status\n  };\n}\n\n// Helper function to create newsletter entry\nexport function createNewsletterEntry(\n  email: string,\n  status: 'success' | 'failed'\n): NewsletterEntry {\n  console.log(`🔍 DEBUG: createNewsletterEntry called for ${email} with status ${status}`);\n  return {\n    timestamp: getCurrentTimestamp(),\n    email: email || '',\n    subscription_status: status\n  };\n}\n"}