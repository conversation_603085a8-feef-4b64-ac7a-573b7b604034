{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-csv-direct.js"}, "modifiedCode": "// Direct test of CSV logging functionality\nimport { \n  logContactFormSubmission, \n  logNewsletterSubscription,\n  createContactFormEntry,\n  createNewsletterEntry\n} from './server/csvLogger.js';\n\nasync function testCsvDirect() {\n  console.log(\"🧪 Testing CSV Logger Directly...\\n\");\n\n  try {\n    // Test contact form logging\n    console.log(\"📧 Testing Contact Form CSV Logging...\");\n    const contactEntry = createContactFormEntry(\n      'Direct Test User',\n      '<EMAIL>',\n      'Test Company',\n      'web-design',\n      '15k-50k',\n      'This is a direct test of CSV logging with commas, \"quotes\", and special chars!',\n      'success'\n    );\n    \n    await logContactFormSubmission(contactEntry);\n    console.log(\"✅ Contact form entry logged\");\n\n    // Test newsletter logging\n    console.log(\"\\n📰 Testing Newsletter CSV Logging...\");\n    const newsletterEntry = createNewsletterEntry('<EMAIL>', 'success');\n    \n    await logNewsletterSubscription(newsletterEntry);\n    console.log(\"✅ Newsletter entry logged\");\n\n    console.log(\"\\n🎉 Direct CSV logging test completed!\");\n    \n  } catch (error) {\n    console.error(\"❌ Error in direct CSV test:\", error);\n  }\n}\n\ntestCsvDirect();\n"}