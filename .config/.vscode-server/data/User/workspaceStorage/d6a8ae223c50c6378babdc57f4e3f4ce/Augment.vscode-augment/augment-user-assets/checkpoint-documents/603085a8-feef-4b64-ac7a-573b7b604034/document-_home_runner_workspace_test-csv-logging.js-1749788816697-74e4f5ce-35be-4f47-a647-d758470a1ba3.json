{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-csv-logging.js"}, "originalCode": "// Test script to verify CSV logging functionality\nimport fs from 'fs';\nimport path from 'path';\n\nasync function testCsvLogging() {\n  console.log(\"🧪 Testing CSV Logging Functionality...\\n\");\n\n  // Test contact form submission\n  console.log(\"📧 Testing Contact Form with CSV Logging...\");\n  try {\n    const contactResponse = await fetch('http://localhost:5000/api/contact', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        name: 'CSV Test User',\n        email: '<EMAIL>',\n        company: 'Test Company Ltd',\n        project: 'web-design',\n        budget: '15k-50k',\n        message: 'This is a test message to verify CSV logging functionality. It contains commas, \"quotes\", and special characters!'\n      }),\n    });\n\n    const contactResult = await contactResponse.json();\n    \n    if (contactResponse.ok) {\n      console.log(\"✅ Contact Form: SUCCESS\");\n      console.log(\"Message:\", contactResult.message);\n    } else {\n      console.log(\"❌ Contact Form: FAILED\");\n      console.log(\"Error:\", contactResult.message);\n    }\n  } catch (error) {\n    console.log(\"❌ Contact Form: ERROR\");\n    console.log(\"Error:\", error.message);\n  }\n\n  // Wait a moment for file operations\n  await new Promise(resolve => setTimeout(resolve, 1000));\n\n  // Test newsletter subscription\n  console.log(\"\\n📰 Testing Newsletter Subscription with CSV Logging...\");\n  try {\n    const newsletterResponse = await fetch('http://localhost:5000/api/newsletter', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        email: '<EMAIL>'\n      }),\n    });\n\n    const newsletterResult = await newsletterResponse.json();\n    \n    if (newsletterResponse.ok) {\n      console.log(\"✅ Newsletter: SUCCESS\");\n      console.log(\"Message:\", newsletterResult.message);\n    } else {\n      console.log(\"❌ Newsletter: FAILED\");\n      console.log(\"Error:\", newsletterResult.message);\n    }\n  } catch (error) {\n    console.log(\"❌ Newsletter: ERROR\");\n    console.log(\"Error:\", error.message);\n  }\n\n  // Wait a moment for file operations\n  await new Promise(resolve => setTimeout(resolve, 1000));\n\n  // Check if CSV files were created and contain data\n  console.log(\"\\n📊 Checking CSV Files...\");\n  \n  const logsDir = path.resolve(process.cwd(), 'server', 'logs');\n  const contactCsvPath = path.join(logsDir, 'contact-enquiries.csv');\n  const newsletterCsvPath = path.join(logsDir, 'newsletter-subscriptions.csv');\n\n  // Check contact enquiries CSV\n  try {\n    if (fs.existsSync(contactCsvPath)) {\n      const contactCsvContent = await fs.promises.readFile(contactCsvPath, 'utf8');\n      const contactLines = contactCsvContent.trim().split('\\n');\n      \n      console.log(\"✅ Contact Enquiries CSV:\");\n      console.log(`   📁 File: ${contactCsvPath}`);\n      console.log(`   📊 Lines: ${contactLines.length} (including header)`);\n      console.log(`   📋 Headers: ${contactLines[0]}`);\n      \n      if (contactLines.length > 1) {\n        console.log(`   📝 Latest entry: ${contactLines[contactLines.length - 1].substring(0, 100)}...`);\n      }\n    } else {\n      console.log(\"❌ Contact Enquiries CSV: File not found\");\n    }\n  } catch (error) {\n    console.log(\"❌ Contact Enquiries CSV: Error reading file\", error.message);\n  }\n\n  // Check newsletter subscriptions CSV\n  try {\n    if (fs.existsSync(newsletterCsvPath)) {\n      const newsletterCsvContent = await fs.promises.readFile(newsletterCsvPath, 'utf8');\n      const newsletterLines = newsletterCsvContent.trim().split('\\n');\n      \n      console.log(\"\\n✅ Newsletter Subscriptions CSV:\");\n      console.log(`   📁 File: ${newsletterCsvPath}`);\n      console.log(`   📊 Lines: ${newsletterLines.length} (including header)`);\n      console.log(`   📋 Headers: ${newsletterLines[0]}`);\n      \n      if (newsletterLines.length > 1) {\n        console.log(`   📝 Latest entry: ${newsletterLines[newsletterLines.length - 1]}`);\n      }\n    } else {\n      console.log(\"❌ Newsletter Subscriptions CSV: File not found\");\n    }\n  } catch (error) {\n    console.log(\"❌ Newsletter Subscriptions CSV: Error reading file\", error.message);\n  }\n\n  console.log(\"\\n\" + \"=\".repeat(60));\n  console.log(\"🎉 CSV Logging Test Complete!\");\n  console.log(\"📧 Check <EMAIL> for test emails\");\n  console.log(\"📊 Check server/logs/ directory for CSV files\");\n  console.log(\"🚀 All functionality maintained with CSV logging added!\");\n}\n\n// Run the test\ntestCsvLogging().catch(console.error);\n"}