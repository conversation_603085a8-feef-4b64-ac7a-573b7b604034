{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-text-logging.js"}, "originalCode": "// Direct test of text logging functionality\nimport {\n  logContactFormSubmission,\n  logNewsletterSubscription,\n  createContactFormEntry,\n  createNewsletterEntry\n} from './server/csvLogger.js';\nimport fs from 'fs';\nimport path from 'path';\n\nasync function testTextLogging() {\n  console.log(\"🧪 Testing Text Logger Directly...\\n\");\n\n  try {\n    // Test contact form logging\n    console.log(\"📧 Testing Contact Form Text Logging...\");\n    const contactEntry = createContactFormEntry(\n      'Direct Text Test User',\n      '<EMAIL>',\n      'Test Company',\n      'web-design',\n      '15k-50k',\n      'This is a direct test of text logging with special chars: commas, \"quotes\", and newlines!\\nSecond line here.',\n      'success'\n    );\n    \n    await logContactFormSubmission(contactEntry);\n    console.log(\"✅ Contact form entry logged\");\n\n    // Test newsletter logging\n    console.log(\"\\n📰 Testing Newsletter Text Logging...\");\n    const newsletterEntry = createNewsletterEntry('<EMAIL>', 'success');\n    \n    await logNewsletterSubscription(newsletterEntry);\n    console.log(\"✅ Newsletter entry logged\");\n\n    console.log(\"\\n🎉 Direct text logging test completed!\");\n    \n    // Check if files were created\n    console.log(\"\\n📁 Checking log files...\");\n    \n    const logsDir = path.join(process.cwd(), 'server', 'logs');\n    const contactFile = path.join(logsDir, 'contact-form-submissions.txt');\n    const newsletterFile = path.join(logsDir, 'newsletter-subscriptions.txt');\n    \n    if (fs.existsSync(contactFile)) {\n      const contactContent = await fs.promises.readFile(contactFile, 'utf8');\n      console.log(\"✅ Contact log file exists:\");\n      console.log(contactContent);\n    } else {\n      console.log(\"❌ Contact log file not found\");\n    }\n    \n    if (fs.existsSync(newsletterFile)) {\n      const newsletterContent = await fs.promises.readFile(newsletterFile, 'utf8');\n      console.log(\"✅ Newsletter log file exists:\");\n      console.log(newsletterContent);\n    } else {\n      console.log(\"❌ Newsletter log file not found\");\n    }\n    \n  } catch (error) {\n    console.error(\"❌ Error in direct text test:\", error);\n  }\n}\n\ntestTextLogging();\n", "modifiedCode": "// Direct test of text logging functionality\nimport {\n  logContactFormSubmission,\n  logNewsletterSubscription,\n  createContactFormEntry,\n  createNewsletterEntry\n} from './server/csvLogger.js';\nimport fs from 'fs';\nimport path from 'path';\n\nasync function testTextLogging() {\n  console.log(\"🧪 Testing Text Logger Directly...\\n\");\n\n  try {\n    // Test contact form logging\n    console.log(\"📧 Testing Contact Form Text Logging...\");\n    const contactEntry = createContactFormEntry(\n      'Direct Text Test User',\n      '<EMAIL>',\n      'Test Company',\n      'web-design',\n      '15k-50k',\n      'This is a direct test of text logging with special chars: commas, \"quotes\", and newlines!\\nSecond line here.',\n      'success'\n    );\n    \n    await logContactFormSubmission(contactEntry);\n    console.log(\"✅ Contact form entry logged\");\n\n    // Test newsletter logging\n    console.log(\"\\n📰 Testing Newsletter Text Logging...\");\n    const newsletterEntry = createNewsletterEntry('<EMAIL>', 'success');\n    \n    await logNewsletterSubscription(newsletterEntry);\n    console.log(\"✅ Newsletter entry logged\");\n\n    console.log(\"\\n🎉 Direct text logging test completed!\");\n    \n    // Check if files were created\n    console.log(\"\\n📁 Checking log files...\");\n    \n    const logsDir = path.join(process.cwd(), 'server', 'logs');\n    const contactFile = path.join(logsDir, 'contact-form-submissions.txt');\n    const newsletterFile = path.join(logsDir, 'newsletter-subscriptions.txt');\n    \n    if (fs.existsSync(contactFile)) {\n      const contactContent = await fs.promises.readFile(contactFile, 'utf8');\n      console.log(\"✅ Contact log file exists:\");\n      console.log(contactContent);\n    } else {\n      console.log(\"❌ Contact log file not found\");\n    }\n    \n    if (fs.existsSync(newsletterFile)) {\n      const newsletterContent = await fs.promises.readFile(newsletterFile, 'utf8');\n      console.log(\"✅ Newsletter log file exists:\");\n      console.log(newsletterContent);\n    } else {\n      console.log(\"❌ Newsletter log file not found\");\n    }\n    \n  } catch (error) {\n    console.error(\"❌ Error in direct text test:\", error);\n  }\n}\n\ntestTextLogging();\n"}