2025-06-13 02:12:17.096 [info] Can't use the Electron fetcher in this environment.
2025-06-13 02:12:17.096 [info] Using the Node fetch fetcher.
2025-06-13 02:12:17.096 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-13 02:12:17.096 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-13 02:12:17.096 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-13 02:12:17.096 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-13 02:12:18.644 [info] Logged in as edwardbowman_nbnco
2025-06-13 02:12:19.456 [info] Got Copilot token for edwardbowman_nbnco
2025-06-13 02:12:19.463 [info] activationBlocker from 'languageModelAccess' took for 3110ms
2025-06-13 02:12:20.055 [info] Fetched model metadata in 593ms ed23b602-3c5c-49f5-83c1-a1b68cde91ab
2025-06-13 02:12:20.910 [info] copilot token chat_enabled: true, sku: copilot_for_business_seat
2025-06-13 02:12:20.924 [info] Registering default platform agent...
2025-06-13 02:12:20.925 [info] activationBlocker from 'conversationFeature' took for 4575ms
2025-06-13 02:12:20.925 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-13 02:12:20.925 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-13 02:12:20.925 [info] Successfully registered GitHub PR title and description provider.
2025-06-13 02:12:20.925 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-13 02:12:21.739 [warning] Copilot preview features are disabled by organizational policy. Learn more: https://aka.ms/github-copilot-org-enable-features
2025-06-13 02:12:21.981 [info] Fetched content exclusion rules in 826ms
2025-06-13 02:12:23.020 [info] Fetched content exclusion rules in 1039ms
2025-06-13 02:12:23.030 [info] Fetched content exclusion rules in 1049ms
2025-06-13 02:12:23.047 [info] Fetched content exclusion rules in 1066ms
2025-06-13 02:12:23.060 [info] Fetched content exclusion rules in 1079ms
