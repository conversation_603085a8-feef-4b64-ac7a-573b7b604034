2025-06-13 02:12:17.486 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-13 02:12:17.486 [info] [Activation] Extension version: 0.111.**********
2025-06-13 02:12:18.242 [info] [Authentication] Creating hub for .com
2025-06-13 02:12:18.650 [info] [Activation] Looking for git repository
2025-06-13 02:12:18.650 [info] [Activation] Found 0 repositories during activation
2025-06-13 02:12:18.650 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-13 02:12:18.654 [info] [GitAPI] Registering git provider
2025-06-13 02:12:18.654 [info] [Review+0] Validate state in progress
2025-06-13 02:12:18.654 [info] [Review+0] Validating state...
2025-06-13 02:12:18.874 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 02:12:18.983 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 02:12:18.983 [info] [FolderRepositoryManager+0] Missing upstream check failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 02:12:18.985 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-13 02:12:18.990 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 02:12:18.994 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 02:12:18.996 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 02:12:19.107 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 02:12:19.339 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 02:12:19.339 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 02:12:19.525 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-13 02:12:19.801 [error] [GitHubRepository+0] Error querying GraphQL API (GetSuggestedActors): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 02:14:18.654 [error] [Review+0] Timeout occurred while validating state.
