2025-06-13 02:12:16.377 [info] [main] Log level: Info
2025-06-13 02:12:16.377 [info] [main] Validating found git in: "git"
2025-06-13 02:12:16.377 [info] [main] Using git "2.47.2" from "git"
2025-06-13 02:12:16.377 [info] [Model][doInitialScan] Initial repository scan started
2025-06-13 02:12:16.377 [info] > git rev-parse --show-toplevel [20ms]
2025-06-13 02:12:16.377 [info] > git rev-parse --git-dir --git-common-dir [52ms]
2025-06-13 02:12:16.377 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-13 02:12:16.377 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-13 02:12:16.377 [info] > git rev-parse --show-toplevel [3ms]
2025-06-13 02:12:16.377 [info] > git config --get commit.template [6ms]
2025-06-13 02:12:16.377 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [145ms]
2025-06-13 02:12:16.377 [info] > git rev-parse --show-toplevel [151ms]
2025-06-13 02:12:16.391 [info] > git rev-parse --show-toplevel [8ms]
2025-06-13 02:12:16.940 [info] > git status -z -uall [533ms]
2025-06-13 02:12:16.941 [info] > git rev-parse --show-toplevel [538ms]
2025-06-13 02:12:17.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [626ms]
2025-06-13 02:12:17.075 [info] > git check-ignore -v -z --stdin [130ms]
2025-06-13 02:12:17.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-06-13 02:12:17.080 [info] > git rev-parse --show-toplevel [55ms]
2025-06-13 02:12:17.083 [info] > git config --get commit.template [8ms]
2025-06-13 02:12:17.084 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-06-13 02:12:17.090 [info] > git rev-parse --show-toplevel [7ms]
2025-06-13 02:12:17.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [5ms]
2025-06-13 02:12:17.096 [info] > git rev-parse --show-toplevel [4ms]
2025-06-13 02:12:17.096 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-13 02:12:17.098 [info] > git merge-base refs/heads/main refs/remotes/origin/main [3ms]
2025-06-13 02:12:17.101 [info] > git rev-parse --show-toplevel [3ms]
2025-06-13 02:12:17.103 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-13 02:12:17.105 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [4ms]
2025-06-13 02:12:17.111 [info] > git status -z -uall [4ms]
2025-06-13 02:12:17.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 02:12:17.524 [info] > git show --textconv :client/src/components/Footer.tsx [7ms]
2025-06-13 02:12:17.524 [info] > git ls-files --stage -- client/src/components/Footer.tsx [5ms]
2025-06-13 02:12:17.533 [info] > git cat-file -s 99e8dcf67ae033d29f51d610151f34d0f2fa18ce [6ms]
2025-06-13 02:12:17.683 [info] > git fetch [206ms]
2025-06-13 02:12:17.683 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-13 02:12:17.692 [info] > git config --get commit.template [3ms]
2025-06-13 02:12:17.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-13 02:12:17.708 [info] > git status -z -uall [4ms]
2025-06-13 02:12:17.710 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 02:12:17.898 [info] > git config --get commit.template [5ms]
2025-06-13 02:12:17.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 02:12:17.906 [info] > git status -z -uall [3ms]
2025-06-13 02:12:17.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 02:12:18.051 [info] > git blame --root --incremental 6a082365cab1bc8c8bbfd74d2a0f296d802b1cb5 -- client/src/components/Footer.tsx [2ms]
2025-06-13 02:12:19.106 [info] > git config --get --local branch.main.github-pr-owner-number [116ms]
2025-06-13 02:12:19.107 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 02:12:19.210 [info] > git ls-files --stage -- client/src/components/Footer.tsx [2ms]
2025-06-13 02:12:19.214 [info] > git cat-file -s 99e8dcf67ae033d29f51d610151f34d0f2fa18ce [1ms]
2025-06-13 02:12:19.445 [info] > git show --textconv :client/src/components/Footer.tsx [5ms]
