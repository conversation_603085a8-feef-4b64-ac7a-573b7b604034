2025-06-13 02:15:33.792 [info] Extension host with pid 5183 started
2025-06-13 02:15:33.792 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock'
2025-06-13 02:15:33.792 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-13 02:15:33.793 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': The lock does not look stale, elapsed: 816 ms, giving up.
2025-06-13 02:15:33.801 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/vscode.lock': Lock acquired.
2025-06-13 02:15:34.698 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-13 02:15:34.700 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-13 02:15:34.700 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescriptreact'
2025-06-13 02:15:34.784 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-13 02:15:35.703 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-13 02:15:36.113 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-13 02:15:36.113 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-13 02:15:36.480 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 02:15:37.015 [info] Eager extensions activated
2025-06-13 02:15:37.015 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 02:15:37.016 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 02:15:37.016 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 02:15:37.017 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 02:15:40.021 [info] ExtensionService#_doActivateExtension GitHub.vscode-pull-request-github, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 02:15:42.649 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-13 02:15:42.649 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-13 02:15:42.649 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-13 02:15:43.225 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 02:16:29.600 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at Pze.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-13 02:26:35.963 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
