2025-06-13 04:00:43.525 [info] Can't use the Electron fetcher in this environment.
2025-06-13 04:00:43.525 [info] Using the Node fetch fetcher.
2025-06-13 04:00:43.525 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-13 04:00:43.525 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-13 04:00:43.525 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-13 04:00:43.525 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-13 04:00:45.361 [info] Logged in as edwardbowman_nbnco
2025-06-13 04:00:46.146 [info] Got Copilot token for edwardbowman_nbnco
2025-06-13 04:00:46.152 [info] activationBlocker from 'languageModelAccess' took for 2709ms
2025-06-13 04:00:46.863 [info] Fetched model metadata in 712ms 0bc88658-53fe-4551-8034-89bb79318391
2025-06-13 04:00:47.709 [info] copilot token chat_enabled: true, sku: copilot_for_business_seat
2025-06-13 04:00:47.725 [info] Registering default platform agent...
2025-06-13 04:00:47.725 [info] activationBlocker from 'conversationFeature' took for 4287ms
2025-06-13 04:00:47.726 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-13 04:00:47.726 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-13 04:00:47.726 [info] Successfully registered GitHub PR title and description provider.
2025-06-13 04:00:47.726 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-13 04:00:48.653 [warning] Copilot preview features are disabled by organizational policy. Learn more: https://aka.ms/github-copilot-org-enable-features
2025-06-13 04:00:48.902 [info] Fetched content exclusion rules in 934ms
2025-06-13 04:00:50.033 [info] Fetched content exclusion rules in 1131ms
2025-06-13 04:00:50.055 [info] Fetched content exclusion rules in 1153ms
