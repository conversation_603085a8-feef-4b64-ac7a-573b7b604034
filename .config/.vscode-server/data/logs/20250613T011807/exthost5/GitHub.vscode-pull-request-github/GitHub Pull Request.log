2025-06-13 04:00:43.998 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-13 04:00:43.999 [info] [Activation] Extension version: 0.111.**********
2025-06-13 04:00:44.702 [info] [Authentication] Creating hub for .com
2025-06-13 04:00:45.198 [info] [Activation] Looking for git repository
2025-06-13 04:00:45.198 [info] [Activation] Found 0 repositories during activation
2025-06-13 04:00:45.198 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-13 04:00:45.201 [info] [GitAPI] Registering git provider
2025-06-13 04:00:45.201 [info] [Review+0] Validate state in progress
2025-06-13 04:00:45.201 [info] [Review+0] Validating state...
2025-06-13 04:00:45.477 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 04:00:45.570 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:00:45.570 [info] [FolderRepositoryManager+0] Missing upstream check failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:00:45.572 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-13 04:00:45.579 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:00:45.584 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:00:45.586 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:00:45.753 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 04:00:45.939 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 04:00:45.940 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 04:00:46.211 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-13 04:00:46.451 [error] [GitHubRepository+0] Error querying GraphQL API (GetSuggestedActors): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 04:02:14.053 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-13 04:02:14.053 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
2025-06-13 04:02:45.201 [error] [Review+0] Timeout occurred while validating state.
2025-06-13 04:09:55.065 [info] [Review+0] Queuing additional validate state
2025-06-13 04:09:55.065 [info] [Review+0] Validating state...
2025-06-13 04:09:55.071 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:09:55.073 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:09:55.074 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:09:55.382 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 04:09:55.382 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 04:09:55.587 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 04:09:55.588 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 04:09:55.588 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-13 04:09:55.599 [info] [Review+0] No matching pull request metadata found locally for current branch main
