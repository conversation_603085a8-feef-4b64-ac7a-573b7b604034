2025-06-13 04:00:42.643 [info] [main] Log level: Info
2025-06-13 04:00:42.643 [info] [main] Validating found git in: "git"
2025-06-13 04:00:42.643 [info] [main] Using git "2.47.2" from "git"
2025-06-13 04:00:42.643 [info] [Model][doInitialScan] Initial repository scan started
2025-06-13 04:00:42.643 [info] > git rev-parse --show-toplevel [78ms]
2025-06-13 04:00:42.643 [info] > git rev-parse --git-dir --git-common-dir [419ms]
2025-06-13 04:00:42.643 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-13 04:00:42.643 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-13 04:00:42.643 [info] > git config --get commit.template [27ms]
2025-06-13 04:00:42.689 [info] > git rev-parse --show-toplevel [46ms]
2025-06-13 04:00:42.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [35ms]
2025-06-13 04:00:43.241 [info] > git rev-parse --show-toplevel [535ms]
2025-06-13 04:00:43.458 [info] > git rev-parse --show-toplevel [162ms]
2025-06-13 04:00:43.477 [info] > git status -z -uall [13ms]
2025-06-13 04:00:43.477 [info] > git rev-parse --show-toplevel [4ms]
2025-06-13 04:00:43.477 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-13 04:00:43.509 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-13 04:00:43.514 [info] > git config --get commit.template [5ms]
2025-06-13 04:00:43.514 [info] > git rev-parse --show-toplevel [10ms]
2025-06-13 04:00:43.514 [info] > git config --get --local branch.main.vscode-merge-base [1ms]
2025-06-13 04:00:43.526 [info] > git rev-parse --show-toplevel [4ms]
2025-06-13 04:00:43.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [8ms]
2025-06-13 04:00:43.535 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-13 04:00:43.537 [info] > git merge-base refs/heads/main refs/remotes/origin/main [6ms]
2025-06-13 04:00:43.542 [info] > git rev-parse --show-toplevel [7ms]
2025-06-13 04:00:43.543 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-13 04:00:43.544 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [3ms]
2025-06-13 04:00:43.551 [info] > git status -z -uall [3ms]
2025-06-13 04:00:43.552 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:00:44.001 [info] > git check-ignore -v -z --stdin [11ms]
2025-06-13 04:00:44.024 [info] > git show --textconv :.env [5ms]
2025-06-13 04:00:44.028 [info] > git ls-files --stage -- .env [5ms]
2025-06-13 04:00:44.032 [info] > git hash-object -t tree /dev/null [4ms]
2025-06-13 04:00:44.032 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-13 04:00:44.037 [info] > git hash-object -t tree /dev/null [5ms]
2025-06-13 04:00:44.037 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-13 04:00:44.462 [info] > git config --get commit.template [2ms]
2025-06-13 04:00:44.579 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [113ms]
2025-06-13 04:00:44.595 [info] > git status -z -uall [11ms]
2025-06-13 04:00:44.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-13 04:00:44.600 [info] > git blame --root --incremental 6a082365cab1bc8c8bbfd74d2a0f296d802b1cb5 -- .env [6ms]
2025-06-13 04:00:44.600 [info] fatal: no such path .env in 6a082365cab1bc8c8bbfd74d2a0f296d802b1cb5
2025-06-13 04:00:45.752 [info] > git config --get --local branch.main.github-pr-owner-number [173ms]
2025-06-13 04:00:45.752 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 04:02:14.034 [info] > git fetch [207ms]
2025-06-13 04:02:14.034 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-13 04:02:14.040 [info] > git config --get commit.template [2ms]
2025-06-13 04:02:14.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:02:14.050 [info] > git status -z -uall [3ms]
2025-06-13 04:02:14.051 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:09:55.051 [info] > git config --get commit.template [6ms]
2025-06-13 04:09:55.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:09:55.060 [info] > git status -z -uall [4ms]
2025-06-13 04:09:55.062 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:09:55.183 [info] > git merge-base refs/heads/main refs/remotes/origin/main [113ms]
2025-06-13 04:09:55.190 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [2ms]
2025-06-13 04:09:55.599 [info] > git config --get --local branch.main.github-pr-owner-number [4ms]
2025-06-13 04:09:55.599 [warning] [Git][config] git config failed: Failed to execute git
