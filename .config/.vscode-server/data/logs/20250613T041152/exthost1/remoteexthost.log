2025-06-13 04:12:03.762 [info] Extension host with pid 14636 started
2025-06-13 04:12:03.762 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': Lock acquired.
2025-06-13 04:12:04.062 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-06-13 04:12:04.063 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-13 04:12:04.064 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-13 04:12:04.274 [error] PendingMigrationError: navigator is now a global in nodejs, please see https://aka.ms/vscode-extensions/navigator for additional info on this error.
    at get (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:1437)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:151:22899
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:151:48239
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:151:66920
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:151:137617
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:151:138691
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:151:175508
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:159:1078
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:159:1320
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:160:1049
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1:263
    at Object.<anonymous> (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:885:8708)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function.<anonymous> (node:internal/modules/cjs/loader:1282:12)
    at e._load (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:810)
    at t._load (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:181:22628)
    at s._load (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:173:23297)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at yY.Cb (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:212:1253)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-06-13 04:12:04.444 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-13 04:12:04.444 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-13 04:12:04.445 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-13 04:12:05.153 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-13 04:12:05.154 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-13 04:12:05.205 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-13 04:12:05.761 [info] Eager extensions activated
2025-06-13 04:12:05.762 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 04:12:05.762 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 04:12:05.764 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 04:12:05.764 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 04:12:08.298 [info] ExtensionService#_doActivateExtension GitHub.vscode-pull-request-github, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 04:12:10.668 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-13 04:12:10.668 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-13 04:12:12.582 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 04:12:15.133 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at Pze.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-13 04:23:32.259 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-13 05:02:39.354 [info] ExtensionService#_doActivateExtension vscode.markdown-math, startup: false, activationEvent: 'api', root cause: vscode.markdown-language-features
2025-06-13 05:03:06.939 [info] ExtensionService#_doActivateExtension vscode.grunt, startup: false, activationEvent: 'onTaskType:grunt'
2025-06-13 05:03:06.940 [info] ExtensionService#_doActivateExtension vscode.gulp, startup: false, activationEvent: 'onTaskType:gulp'
2025-06-13 05:03:06.940 [info] ExtensionService#_doActivateExtension vscode.jake, startup: false, activationEvent: 'onTaskType:jake'
2025-06-13 05:05:06.055 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at Pze.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
