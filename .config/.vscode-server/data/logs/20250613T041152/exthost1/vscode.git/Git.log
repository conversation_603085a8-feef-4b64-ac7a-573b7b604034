2025-06-13 04:12:07.799 [info] [main] Log level: Info
2025-06-13 04:12:07.799 [info] [main] Validating found git in: "git"
2025-06-13 04:12:07.799 [info] [main] Using git "2.47.2" from "git"
2025-06-13 04:12:07.799 [info] [Model][doInitialScan] Initial repository scan started
2025-06-13 04:12:07.799 [info] > git rev-parse --show-toplevel [25ms]
2025-06-13 04:12:07.799 [info] > git rev-parse --git-dir --git-common-dir [1775ms]
2025-06-13 04:12:07.799 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-13 04:12:07.799 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-13 04:12:07.799 [info] > git rev-parse --show-toplevel [21ms]
2025-06-13 04:12:07.799 [info] > git config --get commit.template [24ms]
2025-06-13 04:12:07.799 [info] > git rev-parse --show-toplevel [15ms]
2025-06-13 04:12:07.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [191ms]
2025-06-13 04:12:07.800 [info] > git status -z -uall [5ms]
2025-06-13 04:12:07.801 [info] > git rev-parse --show-toplevel [11ms]
2025-06-13 04:12:07.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:12:07.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [87ms]
2025-06-13 04:12:08.421 [info] > git config --get --local branch.main.vscode-merge-base [511ms]
2025-06-13 04:12:08.429 [info] > git config --get commit.template [604ms]
2025-06-13 04:12:08.429 [info] > git rev-parse --show-toplevel [607ms]
2025-06-13 04:12:08.445 [info] > git check-ignore -v -z --stdin [11ms]
2025-06-13 04:12:08.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [18ms]
2025-06-13 04:12:08.475 [info] > git rev-parse --show-toplevel [36ms]
2025-06-13 04:12:08.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [27ms]
2025-06-13 04:12:08.488 [info] > git merge-base refs/heads/main refs/remotes/origin/main [34ms]
2025-06-13 04:12:08.500 [info] > git rev-parse --show-toplevel [14ms]
2025-06-13 04:12:08.585 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [86ms]
2025-06-13 04:12:08.746 [info] > git rev-parse --show-toplevel [239ms]
2025-06-13 04:12:08.749 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-13 04:12:08.781 [info] > git status -z -uall [162ms]
2025-06-13 04:12:08.825 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [201ms]
2025-06-13 04:12:09.301 [info] > git show --textconv :.env [19ms]
2025-06-13 04:12:09.342 [info] > git ls-files --stage -- .env [56ms]
2025-06-13 04:12:09.357 [info] > git hash-object -t tree /dev/null [8ms]
2025-06-13 04:12:09.357 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-13 04:12:09.363 [info] > git hash-object -t tree /dev/null [10ms]
2025-06-13 04:12:09.363 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/.env.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-13 04:12:09.867 [info] > git config --get commit.template [3ms]
2025-06-13 04:12:09.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 04:12:09.896 [info] > git status -z -uall [4ms]
2025-06-13 04:12:09.898 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:12:11.580 [info] > git config --get --local branch.main.github-pr-owner-number [162ms]
2025-06-13 04:12:11.580 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 04:12:16.820 [info] > git fetch [222ms]
2025-06-13 04:12:16.820 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-13 04:12:16.830 [info] > git config --get commit.template [4ms]
2025-06-13 04:12:16.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:12:16.838 [info] > git status -z -uall [3ms]
2025-06-13 04:12:16.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:12:21.799 [info] > git config --get commit.template [5ms]
2025-06-13 04:12:21.800 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:12:21.806 [info] > git status -z -uall [3ms]
2025-06-13 04:12:21.808 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:12:26.821 [info] > git config --get commit.template [5ms]
2025-06-13 04:12:26.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:12:26.830 [info] > git status -z -uall [4ms]
2025-06-13 04:12:26.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:13:57.933 [info] > git config --get commit.template [9ms]
2025-06-13 04:13:57.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:13:57.950 [info] > git status -z -uall [9ms]
2025-06-13 04:13:57.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 04:14:02.964 [info] > git config --get commit.template [4ms]
2025-06-13 04:14:02.965 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:14:02.972 [info] > git status -z -uall [4ms]
2025-06-13 04:14:02.973 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:14:07.986 [info] > git config --get commit.template [4ms]
2025-06-13 04:14:07.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:14:07.995 [info] > git status -z -uall [4ms]
2025-06-13 04:14:07.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:14:25.950 [info] > git config --get commit.template [4ms]
2025-06-13 04:14:25.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:14:25.957 [info] > git status -z -uall [3ms]
2025-06-13 04:14:25.958 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:15:35.562 [info] > git config --get commit.template [2ms]
2025-06-13 04:15:35.568 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:15:35.575 [info] > git status -z -uall [3ms]
2025-06-13 04:15:35.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:15:40.818 [info] > git config --get commit.template [3ms]
2025-06-13 04:15:40.819 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:15:40.826 [info] > git status -z -uall [4ms]
2025-06-13 04:15:40.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:15:45.836 [info] > git config --get commit.template [1ms]
2025-06-13 04:15:45.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:15:45.849 [info] > git status -z -uall [5ms]
2025-06-13 04:15:45.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:15:50.860 [info] > git config --get commit.template [3ms]
2025-06-13 04:15:50.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:15:50.869 [info] > git status -z -uall [4ms]
2025-06-13 04:15:50.870 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:15:55.880 [info] > git config --get commit.template [4ms]
2025-06-13 04:15:55.882 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:15:55.888 [info] > git status -z -uall [3ms]
2025-06-13 04:15:55.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:16:00.901 [info] > git config --get commit.template [4ms]
2025-06-13 04:16:00.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:16:00.909 [info] > git status -z -uall [3ms]
2025-06-13 04:16:00.910 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:16:05.918 [info] > git config --get commit.template [1ms]
2025-06-13 04:16:05.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:16:05.929 [info] > git status -z -uall [3ms]
2025-06-13 04:16:05.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:16:10.945 [info] > git config --get commit.template [4ms]
2025-06-13 04:16:10.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:16:10.954 [info] > git status -z -uall [5ms]
2025-06-13 04:16:10.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:16:15.965 [info] > git config --get commit.template [4ms]
2025-06-13 04:16:15.965 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:16:15.972 [info] > git status -z -uall [4ms]
2025-06-13 04:16:15.973 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:16:20.984 [info] > git config --get commit.template [4ms]
2025-06-13 04:16:20.985 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:16:20.992 [info] > git status -z -uall [4ms]
2025-06-13 04:16:20.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:16:26.003 [info] > git config --get commit.template [4ms]
2025-06-13 04:16:26.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:16:26.010 [info] > git status -z -uall [3ms]
2025-06-13 04:16:26.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:16:31.021 [info] > git config --get commit.template [3ms]
2025-06-13 04:16:31.023 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:16:31.031 [info] > git status -z -uall [5ms]
2025-06-13 04:16:31.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:16:36.041 [info] > git config --get commit.template [2ms]
2025-06-13 04:16:36.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:16:36.069 [info] > git status -z -uall [12ms]
2025-06-13 04:16:36.072 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 04:16:41.089 [info] > git config --get commit.template [6ms]
2025-06-13 04:16:41.090 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:16:41.105 [info] > git status -z -uall [7ms]
2025-06-13 04:16:41.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 04:16:46.118 [info] > git config --get commit.template [6ms]
2025-06-13 04:16:46.120 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:16:46.131 [info] > git status -z -uall [5ms]
2025-06-13 04:16:46.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:16:51.146 [info] > git config --get commit.template [6ms]
2025-06-13 04:16:51.148 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:16:51.158 [info] > git status -z -uall [5ms]
2025-06-13 04:16:51.158 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:16:56.169 [info] > git config --get commit.template [4ms]
2025-06-13 04:16:56.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:16:56.177 [info] > git status -z -uall [3ms]
2025-06-13 04:16:56.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:17:01.189 [info] > git config --get commit.template [3ms]
2025-06-13 04:17:01.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:17:01.197 [info] > git status -z -uall [3ms]
2025-06-13 04:17:01.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:17:06.212 [info] > git config --get commit.template [7ms]
2025-06-13 04:17:06.213 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:17:06.225 [info] > git status -z -uall [6ms]
2025-06-13 04:17:06.227 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:17:11.235 [info] > git config --get commit.template [2ms]
2025-06-13 04:17:11.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:17:11.248 [info] > git status -z -uall [5ms]
2025-06-13 04:17:11.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:17:16.259 [info] > git config --get commit.template [3ms]
2025-06-13 04:17:16.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:17:16.266 [info] > git status -z -uall [4ms]
2025-06-13 04:17:16.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:17:21.279 [info] > git config --get commit.template [4ms]
2025-06-13 04:17:21.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:17:21.287 [info] > git status -z -uall [3ms]
2025-06-13 04:17:21.288 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:17:27.207 [info] > git config --get commit.template [2ms]
2025-06-13 04:17:27.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:17:27.220 [info] > git status -z -uall [4ms]
2025-06-13 04:17:27.222 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:17:32.238 [info] > git config --get commit.template [7ms]
2025-06-13 04:17:32.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:17:32.250 [info] > git status -z -uall [5ms]
2025-06-13 04:17:32.252 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:17:37.268 [info] > git config --get commit.template [7ms]
2025-06-13 04:17:37.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:17:37.280 [info] > git status -z -uall [7ms]
2025-06-13 04:17:37.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 04:17:42.294 [info] > git config --get commit.template [7ms]
2025-06-13 04:17:42.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:17:42.303 [info] > git status -z -uall [4ms]
2025-06-13 04:17:42.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:27:46.235 [info] > git config --get commit.template [9ms]
2025-06-13 04:27:46.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:27:46.249 [info] > git status -z -uall [6ms]
2025-06-13 04:27:46.250 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:28:42.049 [info] > git config --get commit.template [5ms]
2025-06-13 04:28:42.050 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:28:42.057 [info] > git status -z -uall [4ms]
2025-06-13 04:28:42.059 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:28:47.070 [info] > git config --get commit.template [4ms]
2025-06-13 04:28:47.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:28:47.080 [info] > git status -z -uall [3ms]
2025-06-13 04:28:47.080 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:28:52.089 [info] > git config --get commit.template [2ms]
2025-06-13 04:28:52.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:28:52.100 [info] > git status -z -uall [3ms]
2025-06-13 04:28:52.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:28:57.111 [info] > git config --get commit.template [3ms]
2025-06-13 04:28:57.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:28:57.119 [info] > git status -z -uall [4ms]
2025-06-13 04:28:57.119 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:29:02.131 [info] > git config --get commit.template [3ms]
2025-06-13 04:29:02.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:29:02.140 [info] > git status -z -uall [5ms]
2025-06-13 04:29:02.140 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:29:07.150 [info] > git config --get commit.template [3ms]
2025-06-13 04:29:07.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:29:07.158 [info] > git status -z -uall [4ms]
2025-06-13 04:29:07.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:29:12.181 [info] > git config --get commit.template [8ms]
2025-06-13 04:29:12.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-13 04:29:12.208 [info] > git status -z -uall [8ms]
2025-06-13 04:29:12.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 04:29:17.224 [info] > git config --get commit.template [4ms]
2025-06-13 04:29:17.226 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:29:17.232 [info] > git status -z -uall [3ms]
2025-06-13 04:29:17.233 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:29:26.275 [info] > git config --get commit.template [6ms]
2025-06-13 04:29:26.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:29:26.289 [info] > git status -z -uall [6ms]
2025-06-13 04:29:26.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:29:31.299 [info] > git config --get commit.template [3ms]
2025-06-13 04:29:31.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:29:31.306 [info] > git status -z -uall [3ms]
2025-06-13 04:29:31.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:36:08.751 [info] > git config --get commit.template [1ms]
2025-06-13 04:36:08.758 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:36:08.768 [info] > git status -z -uall [4ms]
2025-06-13 04:36:08.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:36:13.782 [info] > git config --get commit.template [4ms]
2025-06-13 04:36:13.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:36:13.794 [info] > git status -z -uall [6ms]
2025-06-13 04:36:13.796 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:37:40.001 [info] > git config --get commit.template [4ms]
2025-06-13 04:37:40.002 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:37:40.010 [info] > git status -z -uall [4ms]
2025-06-13 04:37:40.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:37:45.022 [info] > git config --get commit.template [4ms]
2025-06-13 04:37:45.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:37:45.030 [info] > git status -z -uall [3ms]
2025-06-13 04:37:45.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:37:50.044 [info] > git config --get commit.template [5ms]
2025-06-13 04:37:50.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:37:50.053 [info] > git status -z -uall [3ms]
2025-06-13 04:37:50.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:38:51.235 [info] > git config --get commit.template [6ms]
2025-06-13 04:38:51.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:38:51.245 [info] > git status -z -uall [4ms]
2025-06-13 04:38:51.246 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:38:56.263 [info] > git config --get commit.template [5ms]
2025-06-13 04:38:56.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:38:56.276 [info] > git status -z -uall [5ms]
2025-06-13 04:38:56.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:39:01.292 [info] > git config --get commit.template [4ms]
2025-06-13 04:39:01.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:39:01.303 [info] > git status -z -uall [4ms]
2025-06-13 04:39:01.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:39:06.319 [info] > git config --get commit.template [4ms]
2025-06-13 04:39:06.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:39:06.328 [info] > git status -z -uall [4ms]
2025-06-13 04:39:06.330 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:39:11.342 [info] > git config --get commit.template [3ms]
2025-06-13 04:39:11.343 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:39:11.350 [info] > git status -z -uall [4ms]
2025-06-13 04:39:11.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:39:18.475 [info] > git config --get commit.template [3ms]
2025-06-13 04:39:18.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:39:18.493 [info] > git status -z -uall [5ms]
2025-06-13 04:39:18.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 04:39:24.466 [info] > git config --get commit.template [2ms]
2025-06-13 04:39:24.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:39:24.478 [info] > git status -z -uall [4ms]
2025-06-13 04:39:24.479 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:39:33.489 [info] > git config --get commit.template [4ms]
2025-06-13 04:39:33.490 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:39:33.499 [info] > git status -z -uall [4ms]
2025-06-13 04:39:33.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:39:38.518 [info] > git config --get commit.template [9ms]
2025-06-13 04:39:38.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 04:39:38.533 [info] > git status -z -uall [5ms]
2025-06-13 04:39:38.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:39:43.551 [info] > git config --get commit.template [1ms]
2025-06-13 04:39:43.565 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:39:43.576 [info] > git status -z -uall [6ms]
2025-06-13 04:39:43.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:39:54.487 [info] > git config --get commit.template [1ms]
2025-06-13 04:39:54.494 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 04:39:54.579 [info] > git status -z -uall [82ms]
2025-06-13 04:39:54.579 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [80ms]
2025-06-13 04:39:59.590 [info] > git config --get commit.template [4ms]
2025-06-13 04:39:59.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:39:59.597 [info] > git status -z -uall [3ms]
2025-06-13 04:39:59.598 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:40:04.609 [info] > git config --get commit.template [4ms]
2025-06-13 04:40:04.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:40:04.616 [info] > git status -z -uall [3ms]
2025-06-13 04:40:04.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:40:09.628 [info] > git config --get commit.template [3ms]
2025-06-13 04:40:09.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 04:40:09.646 [info] > git status -z -uall [6ms]
2025-06-13 04:40:09.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 04:40:23.278 [info] > git config --get commit.template [4ms]
2025-06-13 04:40:23.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:40:23.288 [info] > git status -z -uall [6ms]
2025-06-13 04:40:23.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:40:28.300 [info] > git config --get commit.template [4ms]
2025-06-13 04:40:28.301 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:40:28.307 [info] > git status -z -uall [3ms]
2025-06-13 04:40:28.308 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:40:36.681 [info] > git config --get commit.template [2ms]
2025-06-13 04:40:36.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:40:36.696 [info] > git status -z -uall [5ms]
2025-06-13 04:40:36.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:40:45.680 [info] > git config --get commit.template [3ms]
2025-06-13 04:40:45.682 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:40:45.688 [info] > git status -z -uall [3ms]
2025-06-13 04:40:45.689 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:40:54.703 [info] > git config --get commit.template [3ms]
2025-06-13 04:40:54.708 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:40:54.718 [info] > git status -z -uall [5ms]
2025-06-13 04:40:54.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 04:41:03.683 [info] > git config --get commit.template [7ms]
2025-06-13 04:41:03.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:41:03.699 [info] > git status -z -uall [5ms]
2025-06-13 04:41:03.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:41:08.714 [info] > git config --get commit.template [6ms]
2025-06-13 04:41:08.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:41:08.722 [info] > git status -z -uall [3ms]
2025-06-13 04:41:08.724 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:41:13.734 [info] > git config --get commit.template [2ms]
2025-06-13 04:41:13.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:41:13.747 [info] > git status -z -uall [4ms]
2025-06-13 04:41:13.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:41:18.759 [info] > git config --get commit.template [3ms]
2025-06-13 04:41:18.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:41:18.768 [info] > git status -z -uall [4ms]
2025-06-13 04:41:18.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:41:27.704 [info] > git config --get commit.template [6ms]
2025-06-13 04:41:27.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:41:27.711 [info] > git status -z -uall [3ms]
2025-06-13 04:41:27.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:41:36.702 [info] > git config --get commit.template [2ms]
2025-06-13 04:41:36.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:41:36.716 [info] > git status -z -uall [4ms]
2025-06-13 04:41:36.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:41:48.707 [info] > git config --get commit.template [2ms]
2025-06-13 04:41:48.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:41:48.733 [info] > git status -z -uall [9ms]
2025-06-13 04:41:48.733 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:42:03.718 [info] > git config --get commit.template [6ms]
2025-06-13 04:42:03.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:42:03.730 [info] > git status -z -uall [3ms]
2025-06-13 04:42:03.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:42:12.724 [info] > git config --get commit.template [11ms]
2025-06-13 04:42:12.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 04:42:12.767 [info] > git status -z -uall [25ms]
2025-06-13 04:42:12.768 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-13 04:42:18.711 [info] > git config --get commit.template [4ms]
2025-06-13 04:42:18.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:42:18.727 [info] > git status -z -uall [11ms]
2025-06-13 04:42:18.728 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-13 04:42:30.351 [info] > git config --get commit.template [5ms]
2025-06-13 04:42:30.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:42:30.361 [info] > git status -z -uall [5ms]
2025-06-13 04:42:30.362 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:42:35.374 [info] > git config --get commit.template [0ms]
2025-06-13 04:42:35.380 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:42:35.393 [info] > git status -z -uall [6ms]
2025-06-13 04:42:35.395 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:44:15.882 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-13 04:47:18.039 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-13 04:49:18.471 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-13 04:52:40.515 [info] > git config --get commit.template [3ms]
2025-06-13 04:52:40.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:52:40.527 [info] > git status -z -uall [4ms]
2025-06-13 04:52:40.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:52:45.540 [info] > git config --get commit.template [5ms]
2025-06-13 04:52:45.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:52:45.547 [info] > git status -z -uall [4ms]
2025-06-13 04:52:45.548 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:52:50.560 [info] > git config --get commit.template [5ms]
2025-06-13 04:52:50.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:52:50.573 [info] > git status -z -uall [5ms]
2025-06-13 04:52:50.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:52:55.586 [info] > git config --get commit.template [4ms]
2025-06-13 04:52:55.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:52:55.594 [info] > git status -z -uall [4ms]
2025-06-13 04:52:55.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:53:05.509 [info] > git config --get commit.template [2ms]
2025-06-13 04:53:05.518 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:53:05.531 [info] > git status -z -uall [6ms]
2025-06-13 04:53:05.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:53:10.557 [info] > git config --get commit.template [14ms]
2025-06-13 04:53:10.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-13 04:53:10.599 [info] > git status -z -uall [18ms]
2025-06-13 04:53:10.602 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-13 04:53:17.515 [info] > git config --get commit.template [4ms]
2025-06-13 04:53:17.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:53:17.523 [info] > git status -z -uall [4ms]
2025-06-13 04:53:17.524 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:53:22.606 [info] > git config --get commit.template [2ms]
2025-06-13 04:53:22.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:53:22.619 [info] > git status -z -uall [4ms]
2025-06-13 04:53:22.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:53:27.638 [info] > git config --get commit.template [0ms]
2025-06-13 04:53:27.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:53:27.675 [info] > git status -z -uall [8ms]
2025-06-13 04:53:27.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:53:32.688 [info] > git config --get commit.template [4ms]
2025-06-13 04:53:32.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:53:32.696 [info] > git status -z -uall [4ms]
2025-06-13 04:53:32.697 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:53:37.707 [info] > git config --get commit.template [4ms]
2025-06-13 04:53:37.708 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:53:37.714 [info] > git status -z -uall [3ms]
2025-06-13 04:53:37.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:53:42.730 [info] > git config --get commit.template [4ms]
2025-06-13 04:53:42.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:53:42.738 [info] > git status -z -uall [4ms]
2025-06-13 04:53:42.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:53:53.639 [info] > git config --get commit.template [1ms]
2025-06-13 04:53:53.644 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:53:53.653 [info] > git status -z -uall [6ms]
2025-06-13 04:53:53.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:54:02.637 [info] > git config --get commit.template [2ms]
2025-06-13 04:54:02.642 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:54:02.649 [info] > git status -z -uall [4ms]
2025-06-13 04:54:02.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:54:07.662 [info] > git config --get commit.template [3ms]
2025-06-13 04:54:07.663 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:54:07.670 [info] > git status -z -uall [3ms]
2025-06-13 04:54:07.671 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:54:23.771 [info] > git config --get commit.template [7ms]
2025-06-13 04:54:23.773 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 04:54:23.787 [info] > git status -z -uall [8ms]
2025-06-13 04:54:23.788 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 04:54:34.791 [info] > git config --get commit.template [5ms]
2025-06-13 04:54:34.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:54:34.798 [info] > git status -z -uall [3ms]
2025-06-13 04:54:34.799 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:54:43.787 [info] > git config --get commit.template [1ms]
2025-06-13 04:54:43.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:54:43.801 [info] > git status -z -uall [6ms]
2025-06-13 04:54:43.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 04:54:49.790 [info] > git config --get commit.template [2ms]
2025-06-13 04:54:49.796 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:54:49.802 [info] > git status -z -uall [3ms]
2025-06-13 04:54:49.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:55:01.797 [info] > git config --get commit.template [6ms]
2025-06-13 04:55:01.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:55:01.806 [info] > git status -z -uall [4ms]
2025-06-13 04:55:01.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:55:40.808 [info] > git config --get commit.template [5ms]
2025-06-13 04:55:40.808 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:55:40.817 [info] > git status -z -uall [5ms]
2025-06-13 04:55:40.819 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:55:45.829 [info] > git config --get commit.template [3ms]
2025-06-13 04:55:45.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:55:45.837 [info] > git status -z -uall [3ms]
2025-06-13 04:55:45.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:55:52.809 [info] > git config --get commit.template [6ms]
2025-06-13 04:55:52.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:55:52.817 [info] > git status -z -uall [3ms]
2025-06-13 04:55:52.818 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:55:57.839 [info] > git config --get commit.template [8ms]
2025-06-13 04:55:57.840 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:55:57.856 [info] > git status -z -uall [8ms]
2025-06-13 04:55:57.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:56:02.870 [info] > git config --get commit.template [5ms]
2025-06-13 04:56:02.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:56:02.882 [info] > git status -z -uall [7ms]
2025-06-13 04:56:02.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 04:56:07.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-13 04:56:07.923 [info] > git config --get commit.template [32ms]
2025-06-13 04:56:07.938 [info] > git status -z -uall [9ms]
2025-06-13 04:56:07.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 04:56:13.846 [info] > git config --get commit.template [2ms]
2025-06-13 04:56:13.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:56:13.876 [info] > git status -z -uall [7ms]
2025-06-13 04:56:13.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:56:25.850 [info] > git config --get commit.template [8ms]
2025-06-13 04:56:25.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 04:56:25.865 [info] > git status -z -uall [5ms]
2025-06-13 04:56:25.867 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:58:27.838 [info] > git config --get commit.template [7ms]
2025-06-13 04:58:27.839 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 04:58:27.853 [info] > git status -z -uall [6ms]
2025-06-13 04:58:27.854 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 04:58:32.334 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-13 04:58:32.871 [info] > git config --get commit.template [8ms]
2025-06-13 04:58:32.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-13 04:58:32.879 [info] > git status -z -uall [4ms]
2025-06-13 04:58:32.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 04:58:46.903 [info] > git config --get commit.template [6ms]
2025-06-13 04:58:46.904 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 04:58:46.918 [info] > git status -z -uall [7ms]
2025-06-13 04:58:46.919 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:00:06.709 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-13 05:00:39.884 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-13 05:01:05.005 [info] > git config --get commit.template [9ms]
2025-06-13 05:01:05.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:01:05.022 [info] > git status -z -uall [8ms]
2025-06-13 05:01:05.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:01:10.039 [info] > git config --get commit.template [5ms]
2025-06-13 05:01:10.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:01:10.051 [info] > git status -z -uall [5ms]
2025-06-13 05:01:10.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:01:15.068 [info] > git config --get commit.template [6ms]
2025-06-13 05:01:15.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:01:15.084 [info] > git status -z -uall [6ms]
2025-06-13 05:01:15.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 05:01:20.105 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:01:20.106 [info] > git config --get commit.template [9ms]
2025-06-13 05:01:20.117 [info] > git status -z -uall [6ms]
2025-06-13 05:01:20.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:01:25.130 [info] > git config --get commit.template [4ms]
2025-06-13 05:01:25.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:01:25.139 [info] > git status -z -uall [4ms]
2025-06-13 05:01:25.141 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:01:30.154 [info] > git config --get commit.template [6ms]
2025-06-13 05:01:30.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:01:30.166 [info] > git status -z -uall [5ms]
2025-06-13 05:01:30.167 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:01:47.699 [info] > git config --get commit.template [4ms]
2025-06-13 05:01:47.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:01:47.733 [info] > git status -z -uall [12ms]
2025-06-13 05:01:47.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 05:01:51.908 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-13 05:01:52.750 [info] > git config --get commit.template [7ms]
2025-06-13 05:01:52.752 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:01:52.766 [info] > git status -z -uall [7ms]
2025-06-13 05:01:52.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:01:59.644 [info] > git config --get commit.template [2ms]
2025-06-13 05:01:59.652 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:01:59.664 [info] > git status -z -uall [6ms]
2025-06-13 05:01:59.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:02:04.681 [info] > git config --get commit.template [7ms]
2025-06-13 05:02:04.682 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:02:04.697 [info] > git status -z -uall [9ms]
2025-06-13 05:02:04.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:02:18.170 [info] > git config --get commit.template [7ms]
2025-06-13 05:02:18.170 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:02:18.183 [info] > git status -z -uall [6ms]
2025-06-13 05:02:18.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:02:23.212 [info] > git config --get commit.template [13ms]
2025-06-13 05:02:23.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:02:23.233 [info] > git status -z -uall [10ms]
2025-06-13 05:02:23.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 05:02:31.556 [info] > git config --get commit.template [7ms]
2025-06-13 05:02:31.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:02:31.572 [info] > git status -z -uall [6ms]
2025-06-13 05:02:31.573 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:02:36.591 [info] > git config --get commit.template [7ms]
2025-06-13 05:02:36.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:02:36.608 [info] > git status -z -uall [10ms]
2025-06-13 05:02:36.609 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:02:41.623 [info] > git config --get commit.template [4ms]
2025-06-13 05:02:41.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:02:41.635 [info] > git status -z -uall [6ms]
2025-06-13 05:02:41.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:02:47.404 [info] > git config --get commit.template [6ms]
2025-06-13 05:02:47.405 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:02:47.418 [info] > git status -z -uall [6ms]
2025-06-13 05:02:47.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:02:52.434 [info] > git config --get commit.template [7ms]
2025-06-13 05:02:52.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:02:52.450 [info] > git status -z -uall [8ms]
2025-06-13 05:02:52.451 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:03:26.708 [info] > git config --get commit.template [7ms]
2025-06-13 05:03:26.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:03:26.718 [info] > git status -z -uall [4ms]
2025-06-13 05:03:26.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:03:53.873 [info] > git config --get commit.template [5ms]
2025-06-13 05:03:53.874 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:03:53.888 [info] > git status -z -uall [7ms]
2025-06-13 05:03:53.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:03:58.910 [info] > git config --get commit.template [9ms]
2025-06-13 05:03:58.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:03:58.928 [info] > git status -z -uall [9ms]
2025-06-13 05:03:58.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 05:04:03.947 [info] > git config --get commit.template [6ms]
2025-06-13 05:04:03.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:04:03.958 [info] > git status -z -uall [5ms]
2025-06-13 05:04:03.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:04:20.212 [info] > git config --get commit.template [5ms]
2025-06-13 05:04:20.213 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:04:20.222 [info] > git status -z -uall [4ms]
2025-06-13 05:04:20.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:04:25.245 [info] > git config --get commit.template [10ms]
2025-06-13 05:04:25.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:04:25.265 [info] > git status -z -uall [9ms]
2025-06-13 05:04:25.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:04:30.283 [info] > git config --get commit.template [3ms]
2025-06-13 05:04:30.294 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:04:30.313 [info] > git status -z -uall [8ms]
2025-06-13 05:04:30.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:04:38.643 [info] > git config --get commit.template [22ms]
2025-06-13 05:04:38.684 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [42ms]
2025-06-13 05:04:38.710 [info] > git status -z -uall [12ms]
2025-06-13 05:04:38.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:04:56.177 [info] > git config --get commit.template [7ms]
2025-06-13 05:04:56.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:04:56.194 [info] > git status -z -uall [5ms]
2025-06-13 05:04:56.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:05:01.279 [info] > git config --get commit.template [3ms]
2025-06-13 05:05:01.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 05:05:01.324 [info] > git status -z -uall [16ms]
2025-06-13 05:05:01.324 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-13 05:05:13.938 [info] > git config --get commit.template [9ms]
2025-06-13 05:05:13.940 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:05:13.958 [info] > git status -z -uall [14ms]
2025-06-13 05:05:13.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:05:21.771 [info] > git config --get commit.template [12ms]
2025-06-13 05:05:21.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:05:21.808 [info] > git status -z -uall [19ms]
2025-06-13 05:05:21.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 05:05:26.841 [info] > git config --get commit.template [13ms]
2025-06-13 05:05:26.842 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:05:26.865 [info] > git status -z -uall [16ms]
2025-06-13 05:05:26.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:05:31.887 [info] > git config --get commit.template [10ms]
2025-06-13 05:05:31.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:05:31.908 [info] > git status -z -uall [9ms]
2025-06-13 05:05:31.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:05:36.925 [info] > git config --get commit.template [5ms]
2025-06-13 05:05:36.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:05:36.942 [info] > git status -z -uall [10ms]
2025-06-13 05:05:36.943 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:05:41.957 [info] > git config --get commit.template [4ms]
2025-06-13 05:05:41.959 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:05:41.975 [info] > git status -z -uall [12ms]
2025-06-13 05:05:41.976 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 05:07:03.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:07:03.108 [info] > git config --get commit.template [13ms]
2025-06-13 05:07:03.130 [info] > git status -z -uall [12ms]
2025-06-13 05:07:03.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:07:08.156 [info] > git config --get commit.template [12ms]
2025-06-13 05:07:08.157 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:07:08.179 [info] > git status -z -uall [11ms]
2025-06-13 05:07:08.180 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:07:13.204 [info] > git config --get commit.template [10ms]
2025-06-13 05:07:13.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 05:07:13.222 [info] > git status -z -uall [6ms]
2025-06-13 05:07:13.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 05:08:28.269 [info] > git config --get commit.template [11ms]
2025-06-13 05:08:28.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 05:08:28.304 [info] > git status -z -uall [18ms]
2025-06-13 05:08:28.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:08:33.333 [info] > git config --get commit.template [13ms]
2025-06-13 05:08:33.335 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:08:33.362 [info] > git status -z -uall [15ms]
2025-06-13 05:08:33.363 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:08:38.390 [info] > git config --get commit.template [12ms]
2025-06-13 05:08:38.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:08:38.414 [info] > git status -z -uall [12ms]
2025-06-13 05:08:38.416 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:08:43.444 [info] > git config --get commit.template [13ms]
2025-06-13 05:08:43.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:08:43.473 [info] > git status -z -uall [12ms]
2025-06-13 05:08:43.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:08:48.501 [info] > git config --get commit.template [12ms]
2025-06-13 05:08:48.502 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 05:08:48.524 [info] > git status -z -uall [10ms]
2025-06-13 05:08:48.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:08:53.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 05:08:53.547 [info] > git config --get commit.template [10ms]
2025-06-13 05:08:53.561 [info] > git status -z -uall [7ms]
2025-06-13 05:08:53.562 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 05:08:58.578 [info] > git config --get commit.template [5ms]
2025-06-13 05:08:58.579 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 05:08:58.595 [info] > git status -z -uall [10ms]
2025-06-13 05:08:58.605 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
