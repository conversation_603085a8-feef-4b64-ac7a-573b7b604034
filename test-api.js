// Test script for API endpoints

const API_URL = 'http://localhost:5000';

async function testContactFormEndpoint() {
  console.log('🧪 Testing contact form endpoint...');
  
  try {
    const response = await fetch(`${API_URL}/api/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'API Test User',
        email: '<EMAIL>',
        company: 'API Test Co.',
        project: 'web-app',
        budget: '15k-50k',
        message: 'This is a test submission from the API test script.'
      }),
    });
    
    const data = await response.json();
    console.log('Contact form submission response:', data);
    return data.success;
  } catch (error) {
    console.error('Error testing contact form endpoint:', error);
    return false;
  }
}

async function testNewsletterEndpoint() {
  console.log('🧪 Testing newsletter endpoint...');
  
  try {
    const response = await fetch(`${API_URL}/api/newsletter`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      }),
    });
    
    const data = await response.json();
    console.log('Newsletter subscription response:', data);
    return data.success;
  } catch (error) {
    console.error('Error testing newsletter endpoint:', error);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Starting API endpoint tests...');
  
  console.log('\n=== Contact Form Test ===');
  const contactResult = await testContactFormEndpoint();
  
  console.log('\n=== Newsletter Test ===');
  const newsletterResult = await testNewsletterEndpoint();
  
  console.log('\n=== Test Results ===');
  console.log('Contact Form: ' + (contactResult ? '✅ Passed' : '❌ Failed'));
  console.log('Newsletter: ' + (newsletterResult ? '✅ Passed' : '❌ Failed'));
  
  if (contactResult && newsletterResult) {
    console.log('\n✅ All tests passed! Check the logs directory for new entries.');
  } else {
    console.log('\n❌ Some tests failed. See errors above.');
  }
}

// Run tests when the script is executed
runTests();
