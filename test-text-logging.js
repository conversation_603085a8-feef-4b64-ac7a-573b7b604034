// Direct test of text logging functionality
import {
  logContactFormSubmission,
  logNewsletterSubscription,
  createContactFormEntry,
  createNewsletterEntry,
  ensureLogFiles
} from './server/csvLogger.js';
import fs from 'fs';
import path from 'path';

async function testTextLogging() {
  console.log("🧪 Testing Text Logger Directly...\n");

  try {
    // Test contact form logging
    console.log("📧 Testing Contact Form Text Logging...");
    const contactEntry = createContactFormEntry(
      'Direct Text Test User',
      '<EMAIL>',
      'Test Company',
      'web-design',
      '15k-50k',
      'This is a direct test of text logging with special chars: commas, "quotes", and newlines!\nSecond line here.',
      'success'
    );
    
    await logContactFormSubmission(contactEntry);
    console.log("✅ Contact form entry logged");

    // Test newsletter logging
    console.log("\n📰 Testing Newsletter Text Logging...");
    const newsletterEntry = createNewsletterEntry('<EMAIL>', 'success');
    
    await logNewsletterSubscription(newsletterEntry);
    console.log("✅ Newsletter entry logged");

    console.log("\n🎉 Direct text logging test completed!");
    
    // Check if files were created
    console.log("\n📁 Checking log files...");
    
    const logsDir = path.join(process.cwd(), 'server', 'logs');
    const contactFile = path.join(logsDir, 'contact-form-submissions.txt');
    const newsletterFile = path.join(logsDir, 'newsletter-subscriptions.txt');
    
    if (fs.existsSync(contactFile)) {
      const contactContent = await fs.promises.readFile(contactFile, 'utf8');
      console.log("✅ Contact log file exists:");
      console.log(contactContent);
    } else {
      console.log("❌ Contact log file not found");
    }
    
    if (fs.existsSync(newsletterFile)) {
      const newsletterContent = await fs.promises.readFile(newsletterFile, 'utf8');
      console.log("✅ Newsletter log file exists:");
      console.log(newsletterContent);
    } else {
      console.log("❌ Newsletter log file not found");
    }
    
  } catch (error) {
    console.error("❌ Error in direct text test:", error);
  }
}

testTextLogging();
